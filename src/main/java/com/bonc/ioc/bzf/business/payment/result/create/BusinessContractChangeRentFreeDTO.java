package com.bonc.ioc.bzf.business.payment.result.create;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 免租期变更 (contractChangeRentFreeDTO)
 */
@Data
public class BusinessContractChangeRentFreeDTO implements Serializable {

    @ApiModelProperty(value = "变更合同号")
    private String contractCode;

    @ApiModelProperty(value = "协议号")
    private String agreementCode;

    @ApiModelProperty(value = "优先抵扣/退款")
    private String deductionType; // 0:抵扣，1:退款

    @ApiModelProperty(value = "退款渠道")
    private String refundChannel; // 0:线下退款，1:线上退款

    @ApiModelProperty(value = "变更账期开始时间")
    private String changeBillStartDate; // "yyyy-MM-dd"

    @ApiModelProperty(value = "变更账期结束时间")
    private String changeBillEndDate; // "yyyy-MM-dd"

    @ApiModelProperty(value = "商铺信息")
    private List<Room> roomList; // 按照最后生成账单规则传，只改免租部分
} 