package com.bonc.ioc.bzf.business.payment.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.business.payment.dao.BbpmCashCollectionVoucherMapper;
import com.bonc.ioc.bzf.business.payment.dao.BbpmCollectionMapper;
import com.bonc.ioc.bzf.business.payment.entity.BbpmCollectionEntity;
import com.bonc.ioc.bzf.business.payment.enums.PaymentEnums;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipSettlementFeignClient;
import com.bonc.ioc.bzf.business.payment.result.*;
import com.bonc.ioc.bzf.business.payment.service.*;
import com.bonc.ioc.bzf.business.payment.utils.RedisDistributedId;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.dict.session.McpDictSession;
import com.bonc.ioc.common.exception.McpException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SendCollectionServiceImpl implements ISendCollectionService {

    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmCollectionMapper baseMapper;

    @Resource
    private BbpmCashCollectionVoucherMapper bbpmCashCollectionVoucherMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmCollectionService baseService;

    @Resource
    private IBbpmBillCollectionRelationshipService iBbpmBillCollectionRelationshipService;

    @Resource
    private McpDictSession mcpDictSession;

    @Autowired
    RedisDistributedId redisDistributedId;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IBbpmBillCollectionDetailsService iBbpmBillCollectionDetailsService;

    @Value("${yecai.feign}")
    private boolean yecaiFeign;

    @Value("${yecai.url}")
    private String yecaiUrl;

    @Value("${export.maxSize}")
    private Integer exportMaxSize;

    @Resource
    private BfipChargeFeignClient bfipChargeFeignClient;

    @Resource
    private BfipSettlementFeignClient bfipSettlementFeignClient;

    @Resource
    private IBbpmBillManagementService iBbpmBillManagementService;

    /**
     * 现金 单个收款
     *
     * @param vo
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String collectionSingleCash(BbpmCollectionVo vo) {
        // 加锁成功
        BbpmCollectionEntity entity = new BbpmCollectionEntity();
        BeanUtils.copyProperties(vo, entity);
        entity.setCollectionId(null);
        entity.setCollectionNo(String.valueOf(redisDistributedId.nextId("collection")));
        //1未存款
        entity.setDepositStatus("1");
        entity.setChargeDate(new Date());

        //2024年12月冲刺增加  把这次所有账单的待缴金额放备用字段， 现在不需要，怕以后要
        entity.setExt3(vo.getReplacePayAmount()!=null?vo.getReplacePayAmount().toString():null);

        List<BbpmBillCollectionDetailsVo> bbpmBillCollectionDetailsVoList = new ArrayList<>();
        // 调用工银收款接口
        String receiptNo = updateBillOffline(entity, null, bbpmBillCollectionDetailsVoList,vo.getMultiProject(),vo,null);
        //工银返回他们的收款单id入库
        entity.setReceiptNo(receiptNo);

        //商业系统现金收款不用本地保存
        if(PaymentEnums.PROJECTFORMAT_SY.getCode().equals(vo.getProjectFormat())){
            return receiptNo;
        }

        if (!baseService.insert(entity)) {
            log.error("收款失败:" + entity.toString());
            throw new McpException("收款失败");
        } else {
            if (!baseService.saveOperationHisById(entity.getCollectionId(), 1)) {
                log.error("收款后保存历史失败:" + entity.toString());
                throw new McpException("收款后保存历史失败");
            }

            log.debug("收款成功:" + entity.getCollectionId());

            //插入 账单与收款明细表
            for (BbpmBillCollectionDetailsVo bbpmBillCollectionDetailsVo : bbpmBillCollectionDetailsVoList) {
                bbpmBillCollectionDetailsVo.setCollectionId(entity.getCollectionId());
                bbpmBillCollectionDetailsVo.setChargeSubjectPeriod(vo.getChargeSubjectPeriod());
            }
            iBbpmBillCollectionDetailsService.insertBatchRecord(bbpmBillCollectionDetailsVoList);

            return entity.getCollectionId();
        }
    }

    /**
     * 现金 批量收款
     *
     * @param vo
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String collectionBatchCash(BbpmCollectionBatchVo vo) {
        //账单列表
        List<BbpmBillManagementVo> bbpmBillManagementVoList = vo.getBbpmBillManagementVoList();
//        //按照 收费科目当期期次 正序排列
//        bbpmBillManagementVoList = bbpmBillManagementVoList.stream().sorted(Comparator.comparing(BbpmBillManagementVo::getChargeSubjectPeriod)).collect(Collectors.toList());
//
//        /*
//        1.当交费金额小于应收总金额时，则根据账单周期正序进行结算；例如：
//            每期账单1000元，一次性勾选5期账单（第1期至第5期），则应收总金额为5000元，租户实交金额为3500元，
//            则第1、2、3期账单为已支付，第4期账单为部分支付，第5期账单为未支付。
//
//            这里的应收总额 应该对应工银的 待缴金额
//         */
//        //所有账单应收金额总额
//        BigDecimal amountReceivableAll = vo.getAmountReceivable();
//        //所有账单实收金额总额
//        BigDecimal paidInAmountAll = vo.getPaidInAmount();
//        int res = amountReceivableAll.compareTo(paidInAmountAll);
//        //传给工银的实付金额要动态计算
//        if (res == 0) {
//            //相等  实收金额 ==  应收金额
//            for (BbpmBillManagementVo bbpmBillManagementVo : bbpmBillManagementVoList) {
//                //待缴金额 --> 实缴金额
//                bbpmBillManagementVo.setPayedAmount(bbpmBillManagementVo.getReplacePayAmount());
//            }
//        } else {
//            //计算好实收金额的新账单列表,传给工银用
//            List<BbpmBillManagementVo> newBillList = new ArrayList<>();
//            //不相等
//            for (BbpmBillManagementVo bbpmBillManagementVo : bbpmBillManagementVoList) {
//                //待缴金额
//                BigDecimal replacePayAmount = bbpmBillManagementVo.getReplacePayAmount();
//                //所有账单剩余实收总额 = 实收金额 - 每个账单待缴金额
//                BigDecimal surplus = paidInAmountAll.subtract(replacePayAmount);
//                int lessThanOrEqual = surplus.compareTo(BigDecimal.ZERO);
//                //小于0
//                if (lessThanOrEqual == -1) {
//                    bbpmBillManagementVo.setPayedAmount(paidInAmountAll);
//                    newBillList.add(bbpmBillManagementVo);
//                    break;
//                } else if (lessThanOrEqual == 0) {
//                    //等于0
//                    bbpmBillManagementVo.setPayedAmount(replacePayAmount);
//                    newBillList.add(bbpmBillManagementVo);
//                    break;
//                } else if (lessThanOrEqual == 1) {
//                    //大于0
//                    bbpmBillManagementVo.setPayedAmount(replacePayAmount);
//                    newBillList.add(bbpmBillManagementVo);
//                }
//                paidInAmountAll = surplus;
//            }
//            bbpmBillManagementVoList = newBillList;
//        }
//
//        log.info("批量现金收款计算后的账单信息：{}",JSONObject.toJSONString(bbpmBillManagementVoList));

//        //检查所有批量账单上期是否缴费
//        checkBills(bbpmBillManagementVoList);

        //费用项目
        String expenseItemss = bbpmBillManagementVoList.stream().map(BbpmBillManagementVo::getBillChargeSubject).distinct().collect(Collectors.joining(","));
        //账单编号
        String billNos = bbpmBillManagementVoList.stream().map(BbpmBillManagementVo::getBillNo).distinct().collect(Collectors.joining(","));
        //账单周期
        String billCycles = bbpmBillManagementVoList.stream().map(BbpmBillManagementVo::getBillCycle).distinct().collect(Collectors.joining(","));

        //判断是否为跨合同收款，跨合同收款时，一个账单对应一个收款单。 非跨合同收款时，多个账单对应同一个收款单。工银时这样r
        boolean containsDuplicate = true;
        if(bbpmBillManagementVoList.size() > 1){
            long count = bbpmBillManagementVoList.stream()
                    .map(BbpmBillManagementVo::getContractCode)
                    .distinct()
                    .count();
            if(count == 1){
                //都是一样的合同，去重后长度为1
                containsDuplicate = true;
            }else {
                //不是一样的合同,去重后长度大于1
                containsDuplicate = false;
            }
        }

        //没有跨合同 或 只有一个账单时
        if(containsDuplicate){
            //入库实体赋值
            BbpmCollectionEntity entity = new BbpmCollectionEntity();
            entity.setCollectionId(null);
            entity.setCollectionNo(String.valueOf(redisDistributedId.nextId("collection")));
            entity.setBillChargeSubject(expenseItemss);
            entity.setBillNo(billNos);
            entity.setBillCycle(billCycles);
//        entity.setAmountReceivable(vo.getAmountReceivable());
            entity.setChargeMoney(vo.getPaidInAmount());
//        entity.setRemainingAmountPayable(vo.getRemainingAmountPayable());
            entity.setChargeDate(new Date());
//        entity.setCollectionChannel(vo.getCollectionChannel());
            //1未开
            entity.setElectronicVoucher("1");
            entity.setContractCode(bbpmBillManagementVoList.get(0).getContractNo());
//        entity.setTenantCode(bbpmBillManagementVoList.get(0).getTenantCode());
            entity.setTenantName(bbpmBillManagementVoList.get(0).getTenantName());
//        entity.setContactInformation(bbpmBillManagementVoList.get(0).getContactInformation());
//        entity.setProjectId(bbpmBillManagementVoList.get(0).getProjectId());
            entity.setProjectName(bbpmBillManagementVoList.get(0).getProjectName());
            entity.setHouseName(bbpmBillManagementVoList.get(0).getHouseName());
            //1未存款
            entity.setDepositStatus("1");
            entity.setProjectId(bbpmBillManagementVoList.get(0).getProjectId());

            //2024年12月冲刺增加  把这次所有账单的待缴金额放备用字段， 现在不需要，怕以后要
            entity.setExt3(vo.getAmountReceivable()!=null?vo.getAmountReceivable().toString():null);

            List<BbpmBillCollectionDetailsVo> bbpmBillCollectionDetailsVoList = new ArrayList<>();

            // 调用工银收款接口
            String receiptNo = updateBillOffline(entity, bbpmBillManagementVoList, bbpmBillCollectionDetailsVoList,vo.getMultiProject(),null,vo);
            //工银返回他们的收款单id入库
            entity.setReceiptNo(receiptNo);

            //商业系统现金收款不用本地保存
            if(PaymentEnums.PROJECTFORMAT_SY.getCode().equals(bbpmBillManagementVoList.get(0).getProjectFormat())){
                return receiptNo;
            }

            if (!baseService.insert(entity)) {
                log.error("收款失败:" + entity.toString());
                throw new McpException("收款失败");
            } else {
                if (!baseService.saveOperationHisById(entity.getCollectionId(), 1)) {
                    log.error("收款后保存历史失败:" + entity.toString());
                    throw new McpException("收款后保存历史失败");
                }

                log.debug("收款成功:" + entity.getCollectionId());

                //插入明细表
                for (BbpmBillCollectionDetailsVo bbpmBillCollectionDetailsVo : bbpmBillCollectionDetailsVoList) {
                    bbpmBillCollectionDetailsVo.setCollectionId(entity.getCollectionId());
                }
                //2024年12月冲刺增加  过滤调没有收款的记录
                bbpmBillCollectionDetailsVoList = bbpmBillCollectionDetailsVoList.stream()
                        .filter(bbpmBillCollectionDetailsVo -> bbpmBillCollectionDetailsVo.isKeepIt())
                        .collect(Collectors.toList());
                iBbpmBillCollectionDetailsService.insertBatchRecord(bbpmBillCollectionDetailsVoList);

                return entity.getCollectionId();
            }
        }else {
            //跨合同
            // 调用工银收款接口
            String collectionIds = updateBillOfflineV2(bbpmBillManagementVoList,vo.getMultiProject(),vo);

            return collectionIds;
        }
    }

    /**
     * 线下收款 单个
     *
     * @param vo
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String collectionSingleOffline(BbpmCollectionVo vo) {
        CorporateCollectionVo corporateCollectionVo = vo.getCorporateCollectionVo();
        CorporateCollectionRequest corporateCollectionRequest = CorporateCollectionRequest.builder()
                .paymentType(PaymentEnums.PAYMENTTYPE_OFFLINE.getCode())
//                .amount(vo.getChargeMoney())   下面有个if判断重新赋值这个字段
                .amount(corporateCollectionVo.getAmount())
                .totalAmount(corporateCollectionVo.getTotalAmount())
                .receiptNoUrl(corporateCollectionVo.getReceiptNoUrl())
                .transBillNo(corporateCollectionVo.getTransBillNo())
                .uploader(corporateCollectionVo.getUploader())
                .uploadDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date()))
                .payAcctNo(corporateCollectionVo.getPayAcctNo())
                .payer(corporateCollectionVo.getPayer())
//                .recepitAcctNo(corporateCollectionVo.getRecepitAcctNo())
                //对公转账：receiptBankAcct 收款银行账户   --->  recepitAcctNo 收款方账户号  这2个一样意思
                //后加
//                .receiptBankAcct(corporateCollectionVo.getRecepitAcctNo())
//                .receiptBankName(corporateCollectionVo.getReceiptBankName())
//                .receiptBankCode(corporateCollectionVo.getReceiptBankCode())
                //再次加r
                .receiptBankName(corporateCollectionVo.getBankName())
                .receiptBankAcctNo(corporateCollectionVo.getBankAccountNo())
                .receiptBankAccountName(corporateCollectionVo.getBankAccountName())
                .receiptBankBranchName(corporateCollectionVo.getBranchName())

//        recepiter 收款方名称 --》"receiptBankAccountName"
//        recepitAcctNo 收款方账户号 --》"receiptBankAcctNo"
                .recepiter(corporateCollectionVo.getBankAccountName())
                .recepitAcctNo(corporateCollectionVo.getBankAccountNo())

                .transDate(corporateCollectionVo.getTransDate())
                .summary(corporateCollectionVo.getSummary())
                .remark(corporateCollectionVo.getRemark())
                .useage(corporateCollectionVo.getUseage())
                .bankCode(corporateCollectionVo.getBankCode())
                .projectId(vo.getProjectId())
                .transferBankName(corporateCollectionVo.getTransferBankName())
                .receiptBankCode(corporateCollectionVo.getReceiptBankCode())
//                .transfer(CorporateCollectionSubRequest.builder()
//                        .tenantId(vo.getTenantCode())
//                        .contractId(vo.getContractCode())
//                        .billId(vo.getBillNo())
//                        .billAmout(vo.getReplacePayAmount())
//                        .build())
                .cashTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
                .multiProject(vo.getMultiProject())
                //只有单个的线下转账有  --》回退有这个字段吧
                .chargeCode(vo.getChargeCode())

                //2024年12月冲刺增加
                .amountReceivable(vo.getReplacePayAmount())

                .build();

        //单个线下转账时 部分支付 收款金额    重要！！！！！  为啥要这样？？？
        if(StringUtils.isBlank(vo.getChargeCode())){
            corporateCollectionRequest.setAmount(vo.getChargeMoney());
        }

        List<CorporateCollectionSubRequest> transferList = new ArrayList<>();
        if(StringUtils.isBlank(vo.getChargeCode())){
            CorporateCollectionSubRequest transfer = CorporateCollectionSubRequest.builder()
                    .tenantId(vo.getTenantCode())
                    .contractId(vo.getContractCode())
                    .billId(vo.getBillNo())
//                    .billAmout(vo.getReplacePayAmount())
//                    .billAmout(vo.getChargeMoney())
                    //2024年12月冲刺增加
                    .billAmout(BigDecimal.ZERO)
                    .billChargeSubject(vo.getBillChargeSubject())
                    .chargeSubjectPeriod(String.valueOf(vo.getChargeSubjectPeriod()))
                    .billPayableAmount(vo.getReplacePayAmount())

                    .build();
            transferList.add(transfer);
        }else{
            //2024年12月冲刺增加 和工银沟通没有传0
            corporateCollectionRequest.setAmountReceivable(BigDecimal.ZERO);
        }
        corporateCollectionRequest.setTransferList(transferList);

        String code = updateBillOfflinePublic(corporateCollectionRequest);

        return code;
    }

    /**
     * 线下收款 批量
     *
     * @param vo
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String collectionBatchOffline(BbpmCollectionBatchVo vo) {
        CorporateCollectionVo corporateCollectionVo = vo.getCorporateCollectionVo();

//        BigDecimal replacePayAmountAll = vo.getAmountReceivable();
//        BigDecimal amount = corporateCollectionVo.getAmount();
//        int result = amount.compareTo(replacePayAmountAll);

        CorporateCollectionRequest corporateCollectionRequest = CorporateCollectionRequest.builder()
                .paymentType(PaymentEnums.PAYMENTTYPE_OFFLINE.getCode())
//                .amount(vo.getPaidInAmount())
                .amount(corporateCollectionVo.getAmount())
                .receiptNoUrl(corporateCollectionVo.getReceiptNoUrl())
                .transBillNo(corporateCollectionVo.getTransBillNo())
                .uploader(corporateCollectionVo.getUploader())
                .uploadDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date()))
                .payAcctNo(corporateCollectionVo.getPayAcctNo())
                .payer(corporateCollectionVo.getPayer())
//                .recepitAcctNo(corporateCollectionVo.getRecepitAcctNo())
                //对公转账：receiptBankAcct 收款银行账户   --->  recepitAcctNo 收款方账户号  这2个一样意思
                //后加
//                .receiptBankAcct(corporateCollectionVo.getRecepitAcctNo())
//                .receiptBankName(corporateCollectionVo.getReceiptBankName())
//                .receiptBankCode(corporateCollectionVo.getReceiptBankCode())
                //再次加r
                .receiptBankName(corporateCollectionVo.getBankName())
                .receiptBankAcctNo(corporateCollectionVo.getBankAccountNo())
                .receiptBankAccountName(corporateCollectionVo.getBankAccountName())
                .receiptBankBranchName(corporateCollectionVo.getBranchName())

                //        recepiter 收款方名称 --》"receiptBankAccountName"
//        recepitAcctNo 收款方账户号 --》"receiptBankAcctNo"
                .recepiter(corporateCollectionVo.getBankAccountName())
                .recepitAcctNo(corporateCollectionVo.getBankAccountNo())

                .transDate(corporateCollectionVo.getTransDate())
                .summary(corporateCollectionVo.getSummary())
                .remark(corporateCollectionVo.getRemark())
                .useage(corporateCollectionVo.getUseage())
                .bankCode(corporateCollectionVo.getBankCode())
                .projectId(vo.getBbpmBillManagementVoList().get(0).getProjectId())
                .transferBankName(corporateCollectionVo.getTransferBankName())
                .receiptBankCode(corporateCollectionVo.getReceiptBankCode())
//                .receiptBankName(corporateCollectionVo.getReceiptBankName())
                .cashTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
                .totalAmount(corporateCollectionVo.getTotalAmount())
                .multiProject(vo.getMultiProject())

                //2024年12月冲刺增加
                .amountReceivable(vo.getAmountReceivable())

                .build();

        //账单列表赋值
        List<CorporateCollectionSubRequest> transferList = new ArrayList<>();
        List<BbpmBillManagementVo> bbpmBillManagementVoList = vo.getBbpmBillManagementVoList();
//        //按照 收费科目当期期次 正序排列
//        bbpmBillManagementVoList = bbpmBillManagementVoList.stream().sorted(Comparator.comparing(BbpmBillManagementVo::getChargeSubjectPeriod)).collect(Collectors.toList());
//        if (result == 0) {
//            for(BbpmBillManagementVo billManagementVo :  bbpmBillManagementVoList){
//                CorporateCollectionSubRequest corporateCollectionSubRequest = CorporateCollectionSubRequest.builder()
//                        .tenantId(billManagementVo.getTenantCode())
//                        .contractId(billManagementVo.getContractCode())
//                        .billId(billManagementVo.getBillNo())
//                        .billAmout(billManagementVo.getReplacePayAmount())
//                        .build();
//                transferList.add(corporateCollectionSubRequest);
//            }
//        }else{
//            //计算好实收金额的新账单列表,传给工银用
//            List<BbpmBillManagementVo> newBillList = new ArrayList<>();
//            //不相等
//            for (BbpmBillManagementVo bbpmBillManagementVo : bbpmBillManagementVoList) {
//                //待缴金额
//                BigDecimal replacePayAmount = bbpmBillManagementVo.getReplacePayAmount();
//                //所有账单剩余实收总额 = 实收金额 - 每个账单待缴金额
//                BigDecimal surplus = amount.subtract(replacePayAmount);
//                int lessThanOrEqual = surplus.compareTo(BigDecimal.ZERO);
//                //小于0
//                if (lessThanOrEqual == -1) {
//                    //都赋值到这个字段好取值
//                    bbpmBillManagementVo.setPayedAmount(amount);
//                    newBillList.add(bbpmBillManagementVo);
//                    break;
//                } else if (lessThanOrEqual == 0) {
//                    //等于0
//                    bbpmBillManagementVo.setPayedAmount(replacePayAmount);
//                    newBillList.add(bbpmBillManagementVo);
//                    break;
//                } else if (lessThanOrEqual == 1) {
//                    //大于0
//                    bbpmBillManagementVo.setPayedAmount(replacePayAmount);
//                    newBillList.add(bbpmBillManagementVo);
//                }
//                amount = surplus;
//            }
//            bbpmBillManagementVoList = newBillList;
//
//            for(BbpmBillManagementVo billManagementVo :  bbpmBillManagementVoList){
//                CorporateCollectionSubRequest corporateCollectionSubRequest = CorporateCollectionSubRequest.builder()
//                        .tenantId(billManagementVo.getTenantCode())
//                        .contractId(billManagementVo.getContractCode())
//                        .billId(billManagementVo.getBillNo())
//                        .billAmout(billManagementVo.getPayedAmount())
//                        .build();
//                transferList.add(corporateCollectionSubRequest);
//            }
//        }

        for(BbpmBillManagementVo billManagementVo :  bbpmBillManagementVoList){
            CorporateCollectionSubRequest corporateCollectionSubRequest = CorporateCollectionSubRequest.builder()
                    .tenantId(billManagementVo.getTenantCode())
                    .contractId(billManagementVo.getContractCode())
                    .billId(billManagementVo.getBillNo())
                    //2024年12月冲刺增加
                    .billAmout(BigDecimal.ZERO)
                    .billChargeSubject(billManagementVo.getBillChargeSubject())
                    .chargeSubjectPeriod(String.valueOf(billManagementVo.getChargeSubjectPeriod()))
                    .billPayableAmount(billManagementVo.getReplacePayAmount())
                    .build();
            transferList.add(corporateCollectionSubRequest);
        }
        corporateCollectionRequest.setTransferList(transferList);

        String code = updateBillOfflinePublic(corporateCollectionRequest);

        return code;
    }

    /**
     * 支票 单个
     *
     * @param vo
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String collectionSingleCheque(BbpmCollectionVo vo) {
        CorporateCollectionVo corporateCollectionVo = vo.getCorporateCollectionVo();

        BigDecimal replacePayAmount = vo.getReplacePayAmount();
        BigDecimal amount = corporateCollectionVo.getAmount();

        if(StringUtils.isBlank(vo.getChargeCode())){
            int result = amount.compareTo(replacePayAmount);
            if (result > 0) {
                throw new McpException("支票金额"+amount+"大于账单待缴金额"+replacePayAmount);
            }
        }

        CorporateCollectionRequest corporateCollectionRequest = CorporateCollectionRequest.builder()
                .paymentType(PaymentEnums.PAYMENTTYPE_CHEQUE.getCode())
//                .amount(vo.getChargeMoney())
                .amount(corporateCollectionVo.getAmount())
                .receiptNoUrl(corporateCollectionVo.getReceiptNoUrl())
                .checkNo(corporateCollectionVo.getCheckNo())
                .uploader(corporateCollectionVo.getUploader())
                .uploadDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date()))
                .projectId(vo.getProjectId())
                .totalAmount(corporateCollectionVo.getTotalAmount())
//                .transfer(CorporateCollectionSubRequest.builder()
//                        .tenantId(vo.getTenantCode())
//                        .contractId(vo.getContractCode())
//                        .billId(vo.getBillNo())
////                        .billAmout(vo.getReplacePayAmount())
//                        .billAmout(amount)
//                        .build())
                .cashTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
//                .multiProject(vo.getMultiProject())
                //只有单个的线下转账有 --》回退有这个字段吧
                .chargeCode(vo.getChargeCode())

                //2024年12月冲刺增加
                .amountReceivable(vo.getReplacePayAmount())

                .build();

        List<CorporateCollectionSubRequest> transferList = new ArrayList<>();
        if(StringUtils.isBlank(vo.getChargeCode())){
            CorporateCollectionSubRequest transfer = CorporateCollectionSubRequest.builder()
                    .tenantId(vo.getTenantCode())
                    .contractId(vo.getContractCode())
                    .billId(vo.getBillNo())
//                        .billAmout(vo.getReplacePayAmount())
//                    .billAmout(amount)
                    //2024年12月冲刺增加
                    .billAmout(BigDecimal.ZERO)
                    .billChargeSubject(vo.getBillChargeSubject())
                    .chargeSubjectPeriod(String.valueOf(vo.getChargeSubjectPeriod()))
                    .billPayableAmount(vo.getReplacePayAmount())

                    .build();
            transferList.add(transfer);
        }else{
            //2024年12月冲刺增加 和工银沟通没有传0
            corporateCollectionRequest.setAmountReceivable(BigDecimal.ZERO);
        }
        corporateCollectionRequest.setTransferList(transferList);

        String code = updateBillOfflinePublic(corporateCollectionRequest);

        return code;
    }

    /**
     * 支票 批量
     *
     * @param vo
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String collectionBatchCheque(BbpmCollectionBatchVo vo) {
        CorporateCollectionVo corporateCollectionVo = vo.getCorporateCollectionVo();

        //前端传的时候叫应交金额，实际是前端拿replacePayAmount计算后得到
        BigDecimal replacePayAmountAll = vo.getAmountReceivable();
        BigDecimal amount = corporateCollectionVo.getAmount();
        int result = amount.compareTo(replacePayAmountAll);
        if (result > 0) {
            throw new McpException("支票金额"+amount+"大于账单待缴金额"+replacePayAmountAll);
        }

        CorporateCollectionRequest corporateCollectionRequest = CorporateCollectionRequest.builder()
                .paymentType(PaymentEnums.PAYMENTTYPE_CHEQUE.getCode())
//                .amount(vo.getPaidInAmount())
                .amount(corporateCollectionVo.getAmount())
                .receiptNoUrl(corporateCollectionVo.getReceiptNoUrl())
                .checkNo(corporateCollectionVo.getCheckNo())
                .uploader(corporateCollectionVo.getUploader())
                .uploadDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date()))
                .projectId(vo.getBbpmBillManagementVoList().get(0).getProjectId())
                .cashTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
                .totalAmount(corporateCollectionVo.getTotalAmount())
//                .multiProject(vo.getMultiProject())

                //2024年12月冲刺增加
                .amountReceivable(vo.getAmountReceivable())

                .build();

        //账单列表赋值
        List<CorporateCollectionSubRequest> transferList = new ArrayList<>();
        List<BbpmBillManagementVo> bbpmBillManagementVoList = vo.getBbpmBillManagementVoList();
//        //按照 收费科目当期期次 正序排列
//        bbpmBillManagementVoList = bbpmBillManagementVoList.stream().sorted(Comparator.comparing(BbpmBillManagementVo::getChargeSubjectPeriod)).collect(Collectors.toList());
//        if (result == 0) {
//            //支票金额与账单待缴金额一样
//            for(BbpmBillManagementVo billManagementVo :  bbpmBillManagementVoList){
//                CorporateCollectionSubRequest corporateCollectionSubRequest = CorporateCollectionSubRequest.builder()
//                        .tenantId(billManagementVo.getTenantCode())
//                        .contractId(billManagementVo.getContractCode())
//                        .billId(billManagementVo.getBillNo())
//                        .billAmout(billManagementVo.getReplacePayAmount())
//                        .build();
//                transferList.add(corporateCollectionSubRequest);
//            }
//        }else{
//            //计算好实收金额的新账单列表,传给工银用
//            List<BbpmBillManagementVo> newBillList = new ArrayList<>();
//            //不相等
//            for (BbpmBillManagementVo bbpmBillManagementVo : bbpmBillManagementVoList) {
//                //待缴金额
//                BigDecimal replacePayAmount = bbpmBillManagementVo.getReplacePayAmount();
//                //所有账单剩余实收总额 = 实收金额 - 每个账单待缴金额
//                BigDecimal surplus = amount.subtract(replacePayAmount);
//                int lessThanOrEqual = surplus.compareTo(BigDecimal.ZERO);
//                //小于0
//                if (lessThanOrEqual == -1) {
//                    //都赋值到这个字段好取值
//                    bbpmBillManagementVo.setPayedAmount(amount);
//                    newBillList.add(bbpmBillManagementVo);
//                    break;
//                } else if (lessThanOrEqual == 0) {
//                    //等于0
//                    bbpmBillManagementVo.setPayedAmount(replacePayAmount);
//                    newBillList.add(bbpmBillManagementVo);
//                    break;
//                } else if (lessThanOrEqual == 1) {
//                    //大于0
//                    bbpmBillManagementVo.setPayedAmount(replacePayAmount);
//                    newBillList.add(bbpmBillManagementVo);
//                }
//                amount = surplus;
//            }
//            bbpmBillManagementVoList = newBillList;
//
//            for(BbpmBillManagementVo billManagementVo :  bbpmBillManagementVoList){
//                CorporateCollectionSubRequest corporateCollectionSubRequest = CorporateCollectionSubRequest.builder()
//                        .tenantId(billManagementVo.getTenantCode())
//                        .contractId(billManagementVo.getContractCode())
//                        .billId(billManagementVo.getBillNo())
//                        .billAmout(billManagementVo.getPayedAmount())
//                        .build();
//                transferList.add(corporateCollectionSubRequest);
//            }
//        }

        for(BbpmBillManagementVo billManagementVo :  bbpmBillManagementVoList){
            CorporateCollectionSubRequest corporateCollectionSubRequest = CorporateCollectionSubRequest.builder()
                    .tenantId(billManagementVo.getTenantCode())
                    .contractId(billManagementVo.getContractCode())
                    .billId(billManagementVo.getBillNo())
                    //2024年12月冲刺增加
                    .billAmout(BigDecimal.ZERO)
                    .billChargeSubject(billManagementVo.getBillChargeSubject())
                    .chargeSubjectPeriod(String.valueOf(billManagementVo.getChargeSubjectPeriod()))
                    .billPayableAmount(billManagementVo.getReplacePayAmount())
                    .build();
            transferList.add(corporateCollectionSubRequest);
        }

        corporateCollectionRequest.setTransferList(transferList);
        String code = updateBillOfflinePublic(corporateCollectionRequest);

        return code;
    }

    /**
     * 调用 3.23趸租对公收款接口
     * @param corporateCollectionRequest
     * @return
     */
    private String updateBillOfflinePublic(CorporateCollectionRequest corporateCollectionRequest){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<CorporateCollectionRequest> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(corporateCollectionRequest);

        log.info("3.23趸租对公收款接口请求参数:" + parentRequest.toString());
        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info("3.23趸租对公收款接口请求参数json:" + jsonRequest);

        String responseBody = null;
        if (yecaiFeign) {
            responseBody = bfipSettlementFeignClient.updateBillOfflinePublic(parentRequest);
        } else {
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/settlement/v1/public/bill/updateBillByOffline", parentRequest);
        }
        log.info("调用工银3.23趸租对公收款接口返回结果为:" + responseBody);

        if(StringUtils.isBlank(responseBody)){
            throw new McpException("调用*3.23趸租对公收款接口失败,*返回结果为:" + responseBody);
        }

        FaceMdMapResult faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResult.class);

        if(!("00000").equals(faceMdMapResult.getCode())){
            log.error("调用工银3.23趸租对公收款接口失败:"+responseBody);
            if(faceMdMapResult.getData()!=null && faceMdMapResult.getData().get("Message") != null && StringUtils.isNotBlank(faceMdMapResult.getData().get("Message").toString())){
                throw new McpException("*提示:"+faceMdMapResult.getData().get("Message").toString());
            }else{
                throw new McpException("*提示:"+faceMdMapResult.getMessage());
            }
        }

        return faceMdMapResult.getCode();
    }

    /**
     * 调用工银收款接口 现金
     *
     * @param entity      收款单实体
     * @param newBillList 账单列表
     * @return 返回 chargeIds工银返回的收款单id      返回的收款单是主收款单的，同一个租户的不同账单返回的主收款单的代码是同一个
     */
    private String updateBillOffline(BbpmCollectionEntity entity, List<BbpmBillManagementVo> newBillList, List<BbpmBillCollectionDetailsVo> bbpmBillCollectionDetailsVoList,String multiProject,BbpmCollectionVo bbpmCollectionVo,BbpmCollectionBatchVo bbpmCollectionBatchVo ) {
//        StringBuffer chargeIds = new StringBuffer();
        String chargeId = null;

        String collectionType = "";
        // 调用工银收款接口
        CashOfflineCollectionRequest cashOfflineCollectionRequest = new CashOfflineCollectionRequest();
//        //这里其实应该不用设置这个字段，工银接口有问题
//        cashOfflineCollectionRequest.setSummaryNo("");
        List<CashOfflineCollectionSubRequest> cashOfflineCollectionSubRequestList = new ArrayList<>();
        //单个收款
        if (newBillList == null) {
            collectionType = "单个";

            CashOfflineCollectionSubRequest cashOfflineCollectionSubRequest = new CashOfflineCollectionSubRequest();
            cashOfflineCollectionSubRequest.setTenantId(entity.getTenantCode());
            cashOfflineCollectionSubRequest.setContractId(entity.getContractCode());
            cashOfflineCollectionSubRequest.setDepositNo(entity.getCollectionNo());

            cashOfflineCollectionSubRequest.setBillId(entity.getBillNo());
//            cashOfflineCollectionSubRequest.setCashAmout(entity.getChargeMoney());//
            cashOfflineCollectionSubRequest.setCashAmout(BigDecimal.ZERO);

            cashOfflineCollectionSubRequest.setCashTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(entity.getChargeDate()));

            //2024年12月冲刺增加 里面的
            cashOfflineCollectionSubRequest.setBillChargeSubject(entity.getBillChargeSubject());
            cashOfflineCollectionSubRequest.setChargeSubjectPeriod(String.valueOf(bbpmCollectionVo.getChargeSubjectPeriod()));
            cashOfflineCollectionSubRequest.setBillPayableAmount(bbpmCollectionVo.getReplacePayAmount());

            cashOfflineCollectionSubRequestList.add(cashOfflineCollectionSubRequest);

            //2024年12月冲刺增加 最外层的
            cashOfflineCollectionRequest.setAmount(entity.getChargeMoney());
            cashOfflineCollectionRequest.setAmountReceivable(bbpmCollectionVo.getReplacePayAmount());

            BbpmBillCollectionDetailsVo bbpmBillCollectionDetailsVo = new BbpmBillCollectionDetailsVo();
            bbpmBillCollectionDetailsVo.setBillNo(cashOfflineCollectionSubRequest.getBillId());
            bbpmBillCollectionDetailsVo.setCollectionNo(cashOfflineCollectionSubRequest.getDepositNo());
            bbpmBillCollectionDetailsVo.setCustomerId(cashOfflineCollectionSubRequest.getTenantId());
            bbpmBillCollectionDetailsVo.setContractId(cashOfflineCollectionSubRequest.getContractId());
            bbpmBillCollectionDetailsVo.setCashAmout(cashOfflineCollectionSubRequest.getCashAmout());
            bbpmBillCollectionDetailsVo.setCashTime(entity.getChargeDate());

            //2024年12月冲刺增加
            bbpmBillCollectionDetailsVo.setBillChargeSubject(cashOfflineCollectionSubRequest.getBillChargeSubject());
            bbpmBillCollectionDetailsVo.setReplacePayAmount(cashOfflineCollectionSubRequest.getBillPayableAmount());

            bbpmBillCollectionDetailsVoList.add(bbpmBillCollectionDetailsVo);
        } else {
            collectionType = "批量";

            for (BbpmBillManagementVo vo : newBillList) {
                CashOfflineCollectionSubRequest cashOfflineCollectionSubRequest = new CashOfflineCollectionSubRequest();

//                cashOfflineCollectionSubRequest.setTenantId(entity.getTenantCode());
//                cashOfflineCollectionSubRequest.setContractId(entity.getContractCode());
                cashOfflineCollectionSubRequest.setTenantId(vo.getTenantCode());
                cashOfflineCollectionSubRequest.setContractId(vo.getContractCode());

                cashOfflineCollectionSubRequest.setDepositNo(entity.getCollectionNo());

                cashOfflineCollectionSubRequest.setBillId(String.valueOf(vo.getBillNo()));//
//                cashOfflineCollectionSubRequest.setCashAmout(vo.getPayedAmount());//
                cashOfflineCollectionSubRequest.setCashAmout(BigDecimal.ZERO);
                cashOfflineCollectionSubRequest.setCashTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(entity.getChargeDate()));

                //2024年12月冲刺增加 里面的
                cashOfflineCollectionSubRequest.setBillChargeSubject(vo.getBillChargeSubject());
                cashOfflineCollectionSubRequest.setChargeSubjectPeriod(String.valueOf(vo.getChargeSubjectPeriod()));
                cashOfflineCollectionSubRequest.setBillPayableAmount(vo.getReplacePayAmount());

                cashOfflineCollectionSubRequestList.add(cashOfflineCollectionSubRequest);

                BbpmBillCollectionDetailsVo bbpmBillCollectionDetailsVo = new BbpmBillCollectionDetailsVo();
                bbpmBillCollectionDetailsVo.setBillNo(cashOfflineCollectionSubRequest.getBillId());
                bbpmBillCollectionDetailsVo.setCollectionNo(cashOfflineCollectionSubRequest.getDepositNo());
                bbpmBillCollectionDetailsVo.setCustomerId(cashOfflineCollectionSubRequest.getTenantId());
                bbpmBillCollectionDetailsVo.setContractId(cashOfflineCollectionSubRequest.getContractId());
                bbpmBillCollectionDetailsVo.setCashAmout(cashOfflineCollectionSubRequest.getCashAmout());
                bbpmBillCollectionDetailsVo.setCashTime(entity.getChargeDate());
                bbpmBillCollectionDetailsVo.setChargeSubjectPeriod(vo.getChargeSubjectPeriod());

                //2024年12月冲刺增加
                bbpmBillCollectionDetailsVo.setBillChargeSubject(cashOfflineCollectionSubRequest.getBillChargeSubject());
                bbpmBillCollectionDetailsVo.setReplacePayAmount(cashOfflineCollectionSubRequest.getBillPayableAmount());

                bbpmBillCollectionDetailsVoList.add(bbpmBillCollectionDetailsVo);
            }

            //2024年12月冲刺增加 最外层的
            cashOfflineCollectionRequest.setAmount(bbpmCollectionBatchVo.getPaidInAmount());
            cashOfflineCollectionRequest.setAmountReceivable(bbpmCollectionBatchVo.getAmountReceivable());
        }
        cashOfflineCollectionRequest.setProjectId(entity.getProjectId());
        cashOfflineCollectionRequest.setOfflineList(cashOfflineCollectionSubRequestList);
//        cashOfflineCollectionRequest.setMultiProject(multiProject);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<CashOfflineCollectionRequest> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(cashOfflineCollectionRequest);

        log.info(collectionType + "收款接口请求参数:" + parentRequest.toString());
        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info(collectionType + "收款接口请求参数json:" + jsonRequest);

        String responseBody = null;
        if (yecaiFeign) {
            responseBody = bfipSettlementFeignClient.updateBillOffline(parentRequest);
        } else {
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/settlement/v1/bill/updateBillOffline", parentRequest);
        }
        log.info(collectionType + "调用工银收款接口返回结果为:" + responseBody);

        //String responseBody ="{\"busiCode\":\"\",\"code\":\"00000\",\"data\":{\"receipt\":\"1222\"},\"message\":\"提交成功\"}";
        FaceMdMapResult cashOfflineCollectionResultFaceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResult.class);

        if (cashOfflineCollectionResultFaceMdMapResult != null && cashOfflineCollectionResultFaceMdMapResult.getData() != null) {

            Object ycMessage = cashOfflineCollectionResultFaceMdMapResult.getData().get("Message");
            if (ycMessage != null && StringUtils.isNotBlank(ycMessage.toString())) {
                log.error(collectionType + "调用工银收款接口失败,工银返回结果为:" + responseBody);
                throw new McpException("*提示:"+ycMessage.toString());
            }

            String depositNo = entity.getCollectionNo();
            List<CashOfflineCollectionResult> cashOfflineCollectionResultList = JSON.parseArray(cashOfflineCollectionResultFaceMdMapResult.getData().get(depositNo).toString(), CashOfflineCollectionResult.class);

            if (cashOfflineCollectionResultList == null || cashOfflineCollectionResultList.size() == 0) {
                log.error(collectionType + "调用工银收款接口失败,工银返回结果为:" + responseBody);
//                throw new McpException(cashOfflineCollectionResultFaceMdMapResult.getMessage());
                throw new McpException(collectionType + "调用*收款接口失败,*返回结果有一个节点为空");
            }

            for (CashOfflineCollectionResult cashOfflineCollectionResult : cashOfflineCollectionResultList) {
                //为空表示这个账单收款失败（重复缴费等情况）
                if (StringUtils.isBlank(cashOfflineCollectionResult.getChargeId())) {
                    log.error(collectionType + "调用工银收款接口失败,工银返回结果为:" + responseBody);
                    throw new McpException("*提示:"+cashOfflineCollectionResult.getMessage());
                } else {
//                    chargeIds.append(cashOfflineCollectionResult.getChargeId());
                    chargeId = cashOfflineCollectionResult.getChargeId();
                    String billId = cashOfflineCollectionResult.getBillId();
                    BigDecimal amount = cashOfflineCollectionResult.getAmount();
                    String message = cashOfflineCollectionResult.getMessage();
                    log.info("解析工银收款成功结果:"+billId+"--"+amount+"--"+message);
                    //2024年12月冲刺增加  cashAmout重新赋值 、加入是否入库标志
                    if(StringUtils.isBlank(billId)){
                        continue;
                    }
                    bbpmBillCollectionDetailsVoList.stream().filter(bbpmBillCollectionDetailsVo -> billId.equals(bbpmBillCollectionDetailsVo.getBillNo())).forEach(bbpmBillCollectionDetailsVo -> {
                        bbpmBillCollectionDetailsVo.setCashAmout(amount);
                        bbpmBillCollectionDetailsVo.setKeepIt(true);
                    });
                }
            }
            return chargeId;
        } else {
            log.error(collectionType + "调用工银收款接口失败,工银返回结果为:" + responseBody);
            throw new McpException(collectionType + "调用*收款接口失败,*返回结果为:" + responseBody);
        }
    }

    /**
     * 调用工银收款接口 现金
     *
     * @param newBillList 账单列表
     * @return 返回 chargeIds工银返回的收款单id      返回的收款单是主收款单的，同一个租户的不同账单返回的主收款单的代码是同一个
     */
    private String updateBillOfflineV2(List<BbpmBillManagementVo> newBillList,String multiProject,BbpmCollectionBatchVo bbpmCollectionBatchVo) {
        List<BbpmBillCollectionDetailsVo> bbpmBillCollectionDetailsVoList = new ArrayList<>();

        Map<String, String> collectionNoMap = new HashMap<>();
        //得到合同分组
        Map<String, List<BbpmBillManagementVo>> contractCodeMap = newBillList.stream()
                .collect(Collectors.groupingBy(BbpmBillManagementVo::getContractCode));
        //同一个合同相关
        for (Map.Entry<String, List<BbpmBillManagementVo>> entry : contractCodeMap.entrySet()) {
            String contractCode = entry.getKey();
            //生成一个我们这边自己的一个收款编号
            String collectionNo = String.valueOf(redisDistributedId.nextId("collection"));
            collectionNoMap.put(contractCode,collectionNo);
        }


        Date chargeDate = new Date();
        String projectId = newBillList.get(0).getProjectId();

        //        StringBuffer chargeIds = new StringBuffer();
        String chargeId = null;

        String collectionType = "";
        // 调用工银收款接口
        CashOfflineCollectionRequest cashOfflineCollectionRequest = new CashOfflineCollectionRequest();
//        //这里其实应该不用设置这个字段，工银接口有问题
//        cashOfflineCollectionRequest.setSummaryNo("");
        List<CashOfflineCollectionSubRequest> cashOfflineCollectionSubRequestList = new ArrayList<>();

        collectionType = "批量(跨合同)";

        for (BbpmBillManagementVo vo : newBillList) {
            CashOfflineCollectionSubRequest cashOfflineCollectionSubRequest = new CashOfflineCollectionSubRequest();

//                cashOfflineCollectionSubRequest.setTenantId(entity.getTenantCode());
//                cashOfflineCollectionSubRequest.setContractId(entity.getContractCode());
            cashOfflineCollectionSubRequest.setTenantId(vo.getTenantCode());
            cashOfflineCollectionSubRequest.setContractId(vo.getContractCode());

            String collectionNo = collectionNoMap.get(vo.getContractCode());
            cashOfflineCollectionSubRequest.setDepositNo(collectionNo);

            cashOfflineCollectionSubRequest.setBillId(String.valueOf(vo.getBillNo()));//
//            cashOfflineCollectionSubRequest.setCashAmout(vo.getPayedAmount());//
            cashOfflineCollectionSubRequest.setCashAmout(BigDecimal.ZERO);//
            cashOfflineCollectionSubRequest.setCashTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(chargeDate));

            //2024年12月冲刺增加 里面的
            cashOfflineCollectionSubRequest.setBillChargeSubject(vo.getBillChargeSubject());
            cashOfflineCollectionSubRequest.setChargeSubjectPeriod(String.valueOf(vo.getChargeSubjectPeriod()));
            cashOfflineCollectionSubRequest.setBillPayableAmount(vo.getReplacePayAmount());

            cashOfflineCollectionSubRequestList.add(cashOfflineCollectionSubRequest);

            BbpmBillCollectionDetailsVo bbpmBillCollectionDetailsVo = new BbpmBillCollectionDetailsVo();
            bbpmBillCollectionDetailsVo.setBillNo(cashOfflineCollectionSubRequest.getBillId());
            bbpmBillCollectionDetailsVo.setCollectionNo(cashOfflineCollectionSubRequest.getDepositNo());
            bbpmBillCollectionDetailsVo.setCustomerId(cashOfflineCollectionSubRequest.getTenantId());
            bbpmBillCollectionDetailsVo.setContractId(cashOfflineCollectionSubRequest.getContractId());
            bbpmBillCollectionDetailsVo.setCashAmout(cashOfflineCollectionSubRequest.getCashAmout());
            bbpmBillCollectionDetailsVo.setCashTime(chargeDate);
            bbpmBillCollectionDetailsVo.setChargeSubjectPeriod(vo.getChargeSubjectPeriod());

            //2024年12月冲刺增加
            bbpmBillCollectionDetailsVo.setBillChargeSubject(cashOfflineCollectionSubRequest.getBillChargeSubject());
            bbpmBillCollectionDetailsVo.setReplacePayAmount(cashOfflineCollectionSubRequest.getBillPayableAmount());

            bbpmBillCollectionDetailsVoList.add(bbpmBillCollectionDetailsVo);
        }

        //2024年12月冲刺增加 最外层的
        cashOfflineCollectionRequest.setAmount(bbpmCollectionBatchVo.getPaidInAmount());
        cashOfflineCollectionRequest.setAmountReceivable(bbpmCollectionBatchVo.getAmountReceivable());

        cashOfflineCollectionRequest.setProjectId(projectId);
        cashOfflineCollectionRequest.setOfflineList(cashOfflineCollectionSubRequestList);
//        cashOfflineCollectionRequest.setMultiProject(multiProject);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        ParentRequest<CashOfflineCollectionRequest> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(cashOfflineCollectionRequest);

        log.info(collectionType + "收款接口请求参数:" + parentRequest.toString());
        String jsonRequest = JSONObject.toJSONString(parentRequest);
        log.info(collectionType + "收款接口请求参数json:" + jsonRequest);

        String responseBody = null;
        if (yecaiFeign) {
            responseBody = bfipSettlementFeignClient.updateBillOffline(parentRequest);
        } else {
            responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl+"/settlement/v1/bill/updateBillOffline", parentRequest);
        }
        log.info(collectionType + "调用工银收款接口返回结果为:" + responseBody);

        //String responseBody ="{\"busiCode\":\"\",\"code\":\"00000\",\"data\":{\"receipt\":\"1222\"},\"message\":\"提交成功\"}";
        FaceMdMapResult cashOfflineCollectionResultFaceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResult.class);

        if (cashOfflineCollectionResultFaceMdMapResult != null && cashOfflineCollectionResultFaceMdMapResult.getData() != null) {

            Object ycMessage = cashOfflineCollectionResultFaceMdMapResult.getData().get("Message");
            if (ycMessage != null && StringUtils.isNotBlank(ycMessage.toString())) {
                log.error(collectionType + "调用工银收款接口失败,工银返回结果为:" + responseBody);
                throw new McpException("*提示:"+ycMessage.toString());
            }

            Map<String,String> chargeIdMap = new HashMap<>();
            //同一个合同相关
            for (Map.Entry<String, List<BbpmBillManagementVo>> entry : contractCodeMap.entrySet()) {
                String contractCode = entry.getKey();
                String collectionNo = collectionNoMap.get(contractCode);

                String depositNo = collectionNo;
                if (cashOfflineCollectionResultFaceMdMapResult.getData().get(depositNo) == null) {
                    continue;
                }
                List<CashOfflineCollectionResult> cashOfflineCollectionResultList = JSON.parseArray(cashOfflineCollectionResultFaceMdMapResult.getData().get(depositNo).toString(), CashOfflineCollectionResult.class);

                if (cashOfflineCollectionResultList == null || cashOfflineCollectionResultList.size() == 0) {
                    log.error(collectionType + "调用工银收款接口失败,工银返回结果为:" + responseBody);
//                    throw new McpException(cashOfflineCollectionResultFaceMdMapResult.getMessage());
                    throw new McpException(collectionType + "调用*收款接口失败,*返回结果有一个节点为空");
                }

                for (CashOfflineCollectionResult cashOfflineCollectionResult : cashOfflineCollectionResultList) {
                    //为空表示这个账单收款失败（重复缴费等情况）
                    if (StringUtils.isBlank(cashOfflineCollectionResult.getChargeId())) {
                        log.error(collectionType + "调用工银收款接口失败,工银返回结果为:" + responseBody);
                        throw new McpException("*提示:"+cashOfflineCollectionResult.getMessage());
                    } else {
//                    chargeIds.append(cashOfflineCollectionResult.getChargeId());
                        chargeId = cashOfflineCollectionResult.getChargeId();

                        chargeIdMap.put(String.valueOf(cashOfflineCollectionResult.getBillId()),cashOfflineCollectionResult.getChargeId());

                        String billId = cashOfflineCollectionResult.getBillId();
                        BigDecimal amount = cashOfflineCollectionResult.getAmount();
                        String message = cashOfflineCollectionResult.getMessage();
                        log.info("解析工银收款成功结果:"+billId+"--"+amount+"--"+message);
                        //2024年12月冲刺增加  cashAmout重新赋值 、加入是否入库标志
                        if(StringUtils.isBlank(billId)){
                            continue;
                        }
                        bbpmBillCollectionDetailsVoList.stream().filter(bbpmBillCollectionDetailsVo -> billId.equals(bbpmBillCollectionDetailsVo.getBillNo())).forEach(bbpmBillCollectionDetailsVo -> {
                            bbpmBillCollectionDetailsVo.setCashAmout(amount);
                            bbpmBillCollectionDetailsVo.setKeepIt(true);
                        });
                    }
                }
            }

            //商业系统现金收款不用本地保存
            if (PaymentEnums.PROJECTFORMAT_SY.getCode().equals(newBillList.get(0).getProjectFormat())) {
                return chargeIdMap.get(newBillList.get(0).getBillId());
            }

            String collectionIds = "";

            //同一个合同相关
            for (Map.Entry<String, List<BbpmBillManagementVo>> entry : contractCodeMap.entrySet()) {
                String contractCode = entry.getKey();
                List<BbpmBillManagementVo> bbpmBillManagementVoList = entry.getValue();
                //费用项目
                String expenseItemss = bbpmBillManagementVoList.stream().map(BbpmBillManagementVo::getBillChargeSubject).distinct().collect(Collectors.joining(","));
                //账单编号
                String billNos = bbpmBillManagementVoList.stream().map(BbpmBillManagementVo::getBillNo).distinct().collect(Collectors.joining(","));
                //账单周期
                String billCycles = bbpmBillManagementVoList.stream().map(BbpmBillManagementVo::getBillCycle).distinct().collect(Collectors.joining(","));
                //把bbpmBillManagementVoList中payedAmount求和
                BigDecimal payedAmount = bbpmBillManagementVoList.stream().map(BbpmBillManagementVo::getPayedAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

                //入库实体赋值
                BbpmCollectionEntity entity = new BbpmCollectionEntity();
                entity.setCollectionId(null);
                String collectionNo = collectionNoMap.get(contractCode);
                entity.setCollectionNo(collectionNo);
                entity.setBillChargeSubject(expenseItemss);
                entity.setBillNo(billNos);
                entity.setBillCycle(billCycles);
//        entity.setAmountReceivable(vo.getAmountReceivable());
                //2024年12月冲刺 修改
                entity.setChargeMoney(payedAmount);
//                entity.setChargeMoney(bbpmBillManagementVoList.get(0).getPayedAmount());

//        entity.setRemainingAmountPayable(vo.getRemainingAmountPayable());
                entity.setChargeDate(new Date());
//        entity.setCollectionChannel(vo.getCollectionChannel());
                //1未开
                entity.setElectronicVoucher("1");
                entity.setContractCode(bbpmBillManagementVoList.get(0).getContractNo());
//        entity.setTenantCode(bbpmBillManagementVoList.get(0).getTenantCode());
                entity.setTenantName(bbpmBillManagementVoList.get(0).getTenantName());
//        entity.setContactInformation(bbpmBillManagementVoList.get(0).getContactInformation());
//        entity.setProjectId(bbpmBillManagementVoList.get(0).getProjectId());
                entity.setProjectName(bbpmBillManagementVoList.get(0).getProjectName());
                entity.setHouseName(bbpmBillManagementVoList.get(0).getHouseName());
                //1未存款
                entity.setDepositStatus("1");
                entity.setProjectId(bbpmBillManagementVoList.get(0).getProjectId());

                //2024年12月冲刺增加  把这次所有账单的待缴金额放备用字段， 现在不需要，怕以后要
                entity.setExt3(bbpmCollectionBatchVo.getAmountReceivable()!=null?bbpmCollectionBatchVo.getAmountReceivable().toString():null);

                //工银返回他们的收款单id入库
                if(StringUtils.isBlank(chargeIdMap.get(bbpmBillManagementVoList.get(0).getBillId()))){
                     continue;
                }
                entity.setReceiptNo(chargeIdMap.get(bbpmBillManagementVoList.get(0).getBillId()));

                if (!baseService.insert(entity)) {
                    log.error("收款失败:" + entity.toString());
                    throw new McpException("收款失败");
                } else {
                    if (!baseService.saveOperationHisById(entity.getCollectionId(), 1)) {
                        log.error("收款后保存历史失败:" + entity.toString());
                        throw new McpException("收款后保存历史失败");
                    }

                    log.debug("收款成功:" + entity.getCollectionId());

                    List<BbpmBillCollectionDetailsVo> newDetailsList = new ArrayList<>();
                    //插入明细表
                    for (BbpmBillCollectionDetailsVo bbpmBillCollectionDetailsVo : bbpmBillCollectionDetailsVoList) {
                        if (bbpmBillCollectionDetailsVo.getContractId().equals(contractCode)
                                && bbpmBillCollectionDetailsVo.isKeepIt()) {
                            bbpmBillCollectionDetailsVo.setCollectionId(entity.getCollectionId());
                            newDetailsList.add(bbpmBillCollectionDetailsVo);
                        }
                    }
                    iBbpmBillCollectionDetailsService.insertBatchRecord(newDetailsList);

                    collectionIds += entity.getCollectionId() + ",";
                }
            }

            return collectionIds;
        } else {
            log.error(collectionType + "调用工银收款接口失败,工银返回结果为:" + responseBody);
            throw new McpException(collectionType + "调用*收款接口失败,*返回结果为:" + responseBody);
        }
    }



    public void checkBills(List<BbpmBillManagementVo> bbpmBillManagementVoList) {
        BbpmBillManagementPageVo billManagementPageVo = new BbpmBillManagementPageVo();
        //拼接请求参数
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        billManagementPageVo.setFullPage("true");
        //01 已缴足额支付 02 已缴部分支付 03 未缴
        billManagementPageVo.setBillStatus("02,03");
        //合同编号
        billManagementPageVo.setContractCode(bbpmBillManagementVoList.get(0).getContractCode());//
        billManagementPageVo.setPrimaryChargeCodeFlag("1");
        ParentRequest<BbpmBillManagementPageVo> parentRequest = new ParentRequest<>();
        parentRequest.setTime(sdf.format(new Date()));
        parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
        parentRequest.setData(billManagementPageVo);
        //请求业财接口 重新set值到分页实体、转换
        PageResult<List<BbpmBillManagementPageResultVo>> pageResult = iBbpmBillManagementService.listByContract(parentRequest);
        if (pageResult != null && pageResult.getRows() != null && pageResult.getRows().size() > 0) {
            List<BbpmBillManagementPageResultVo> bbpmBillManagementPageResultVoList = pageResult.getRows();
            //倒序计算这些账单的上期账单是否缴费了
            for (int j = bbpmBillManagementVoList.size() - 1; j >= 0; j--) {
                BbpmBillManagementVo billManagementVo = bbpmBillManagementVoList.get(j);
                //账单周期(从1开始递增)
                Integer chargeSubjectPeriod = billManagementVo.getChargeSubjectPeriod();
                if (chargeSubjectPeriod != null && chargeSubjectPeriod.intValue() != 1) {
                    //上个周期
                    Integer upper = chargeSubjectPeriod - 1;
//                        //账单对应的收费科目
//                        String billChargeSubject = billManagementVo.getBillChargeSubject();
                    //与工银结果对比
                    for (BbpmBillManagementPageResultVo bbpmBillManagementPageResultVo : bbpmBillManagementPageResultVoList) {
                        //上个周期没缴清
                        if (bbpmBillManagementPageResultVo.getChargeSubjectPeriod() != null && upper.intValue() == bbpmBillManagementPageResultVo.getChargeSubjectPeriod().intValue()) {//billChargeSubject.equals(bbpmBillManagementPageResultVo.getBillChargeSubject()
                            //上个周期不在这次收款里
                            for (int k = 0; k < bbpmBillManagementVoList.size(); k++) {
                                BbpmBillManagementVo bbpmBillManagementVo = bbpmBillManagementVoList.get(k);
                                if (bbpmBillManagementVo.getChargeSubjectPeriod() != null && upper.intValue() == bbpmBillManagementVo.getChargeSubjectPeriod().intValue()
                                        && bbpmBillManagementPageResultVo.getBillChargeSubject() != null && bbpmBillManagementPageResultVo.getBillChargeSubject().equals(bbpmBillManagementVo.getBillChargeSubject())) {
                                    break;
                                }
                                if (k == bbpmBillManagementVoList.size() - 1) {
                                    throw new McpException(billManagementVo.getBillCycle() + "的上期账单未缴清");
                                }
                            }

                        }
                    }
                }
            }
        }
    }


    public static void main(String[] args){
        System.out.println(BigDecimal.ZERO);
    }
}
