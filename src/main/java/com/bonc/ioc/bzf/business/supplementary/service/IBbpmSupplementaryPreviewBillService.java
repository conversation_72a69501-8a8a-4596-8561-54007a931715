package com.bonc.ioc.bzf.business.supplementary.service;

import com.bonc.ioc.bzf.business.supplementary.entity.BbpmSupplementaryPreviewBillEntity;
import com.bonc.ioc.common.base.service.IMcpBaseService;

import java.util.List;

import com.bonc.ioc.bzf.business.supplementary.vo.*;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 追加单试算表 服务类
 *
 * <AUTHOR>
 * @date 2025-04-08
 * @change 2025-04-08 by pyj for init
 */
public interface IBbpmSupplementaryPreviewBillService extends IMcpBaseService<BbpmSupplementaryPreviewBillEntity> {
    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    String insertRecord(BbpmSupplementaryPreviewBillVo vo);

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    List<String> insertBatchRecord(List<BbpmSupplementaryPreviewBillVo> voList);

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param billId 需要删除的试算id
     * @return void
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    void removeByIdRecord(String billId);

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param billIdList 需要删除的试算id
     * @return void
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    void removeByIdsRecord(List<String> billIdList);

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的追加单试算表
     * @return void
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    void updateByIdRecord(BbpmSupplementaryPreviewBillVo vo);

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的追加单试算表
     * @return void
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    void updateBatchByIdRecord(List<BbpmSupplementaryPreviewBillVo> voList);

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的追加单试算表
     * @return void
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    void saveByIdRecord(BbpmSupplementaryPreviewBillVo vo);

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的追加单试算表
     * @return void
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    void saveBatchByIdRecord(List<BbpmSupplementaryPreviewBillVo> voList);

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param billId 需要查询的试算id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    BbpmSupplementaryPreviewBillVo selectByIdRecord(String billId);

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    PageResult<List<BbpmSupplementaryPreviewBillPageResultVo>> selectByPageRecord(BbpmSupplementaryPreviewBillPageVo vo);

    /**
     * 根据上级id查询
     *
     * @param parentId 上级id
     * @return 查询结果
     */
    List<BbpmSupplementaryPreviewBillVo> selectListByParentId(String parentId);

    /**
     * 获取导出数据
     *
     * @return 导出数据
     */
    List<BbpmSupplementaryExportVo> getExportData();
}
