package com.bonc.ioc.bzf.business.payment.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DoubleColorBallGenerator {

    /**
     * 生成一组双色球号码
     * @return 包含红球和蓝球的Map
     */
    public static Map<String, List<Integer>> generate() {
        // 红球范围：1-33，随机选出6个不重复的数字
        List<Integer> redBalls = new ArrayList<>();
        for (int i = 1; i <= 33; i++) {
            redBalls.add(i);
        }
        Collections.shuffle(redBalls);
        List<Integer> selectedRedBalls = redBalls.subList(0, 6);
        Collections.sort(selectedRedBalls);

        // 蓝球范围：1-16，随机选出1个数字
        List<Integer> blueBalls = new ArrayList<>();
        for (int i = 1; i <= 16; i++) {
            blueBalls.add(i);
        }
        Collections.shuffle(blueBalls);
        List<Integer> selectedBlueBalls = blueBalls.subList(0, 1);

        // 返回结果
        Map<String, List<Integer>> result = new HashMap<>();
        result.put("red", selectedRedBalls);
        result.put("blue", selectedBlueBalls);
        return result;
    }
}