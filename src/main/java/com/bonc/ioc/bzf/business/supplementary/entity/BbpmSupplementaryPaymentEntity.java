package com.bonc.ioc.bzf.business.supplementary.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;

import com.bonc.ioc.common.validator.inf.*;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 追加账单表 实体类
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
@Data
@TableName("bbpm_supplementary_payment")
@ApiModel(value = "BbpmSupplementaryPaymentEntity对象", description = "追加账单表")
public class BbpmSupplementaryPaymentEntity extends McpBaseEntity implements Serializable {

    /**
     * 追加账单id
     */
    @ApiModelProperty(value = "追加账单id")
    @TableId(value = "payment_id", type = IdType.ASSIGN_UUID)
    private String paymentId;

    /**
     * 上级id
     */
    @ApiModelProperty(value = "上级id")
    private String parentId;

    /**
     * 账单类型(1.周期性账单 2.一次性账单)
     */
    @ApiModelProperty(value = "账单类型(1.周期性账单 2.一次性账单)")
    private String paymentType;

    /**
     * 费用项目(1.租金 2.家具家电租金 3.增值服务费)
     */
    @ApiModelProperty(value = "费用项目(1.租金 2.家具家电租金 3.增值服务费)")
    private String expenseItem;

    /**
     * 费用金额
     */
    @ApiModelProperty(value = "费用金额")
    private String expenseItemMoney;

    /**
     * 缴费周期code(01.月付 02.季度付 03.半年付 04.年付)
     */
    @ApiModelProperty(value = "缴费周期code(01.月付 02.季度付 03.半年付 04.年付)")
    private String paymentCycleCode;

    /**
     * 账单开始日期
     */
    @ApiModelProperty(value = "账单开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate paymentBeginTime;

    /**
     * 账单结束日期
     */
    @ApiModelProperty(value = "账单结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate paymentEndTime;

    /**
     * 追加账单日期
     */
    @ApiModelProperty(value = "追加账单日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate supplementaryTime;

    /**
     * 计费标准code(1.单价 2.一口价 3.月租金百分比)
     */
    @ApiModelProperty(value = "计费标准code(1.单价 2.一口价 3.月租金百分比)")
    private String chargeStandardCode;

    /**
     * 计费标准金额
     */
    @ApiModelProperty(value = "计费标准金额")
    private String chargeStandardMoney;

    /**
     * 计费标准百分比
     */
    @ApiModelProperty(value = "计费标准百分比")
    private String chargeStandardPercent;

    /**
     * 费用备注
     */
    @ApiModelProperty(value = "费用备注")
    private String chargeRemark;

    /**
     * 支付方式(1.企业付 2.个人付 3.比例支付)
     */
    @ApiModelProperty(value = "支付方式(1.企业付 2.个人付 3.比例支付)")
    private String payMode;

    /**
     * 缴费类型(1.比例 2.金额)
     */
    @ApiModelProperty(value = "缴费类型(1.比例 2.金额)")
    private String payType;

    /**
     * 缴费金额
     */
    @ApiModelProperty(value = "缴费金额")
    private String payMoney;

    /**
     * 缴费比例
     */
    @ApiModelProperty(value = "缴费比例")
    private String payPercent;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private String serial;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
    private String delFlag;
}
