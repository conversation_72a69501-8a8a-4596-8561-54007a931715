package com.bonc.ioc.bzf.business.supplementary.controller;

import com.bonc.ioc.bzf.business.supplementary.service.IBbpmSupplementaryInfoService;
import com.bonc.ioc.bzf.business.supplementary.vo.BbpmApproveDetailInfoVo;
import com.bonc.ioc.bzf.business.supplementary.vo.BbpmApproveVo;
import com.bonc.ioc.bzf.business.supplementary.vo.BbpmSupplementaryExportVo;
import com.bonc.ioc.bzf.business.supplementary.vo.BbpmSupplementaryInfoPageResultVo;
import com.bonc.ioc.bzf.business.supplementary.vo.BbpmSupplementaryInfoPageVo;
import com.bonc.ioc.bzf.business.supplementary.vo.BbpmSupplementaryInfoVo;
import com.bonc.ioc.bzf.business.supplementary.vo.BbpmSupplementaryPreviewBillVo;
import com.bonc.ioc.bzf.business.supplementary.vo.BbpmSupplementaryStatisticVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 追加单业务相关 前端控制器
 *
 * <AUTHOR>
 * @since 2025/3/26
 */
@RestController
@RequestMapping("/v2/business/bbpmSupplementaryInfoEntity")
@Api(tags = "追加账单业务相关")
@Validated
public class BbpmBusinessSupplementaryInfoController {

    /**
     * 追加单相关 服务实例
     */
    @Resource
    private IBbpmSupplementaryInfoService supplementaryInfoService;

    /**
     * 新增追加单信息
     *
     * @param vo 追加单信息 vo实体
     * @return 主键id
     */
    @PostMapping(value = "/insertRecord", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "pyj")
    @ApiOperation(value = "新增", notes = "新增全表数据", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    @ApiResponses({@ApiResponse(code = 200, message = "data:新增主键")})
    public AppReply<String> insertRecord(@ApiParam(value = "追加单表", required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbpmSupplementaryInfoVo vo) {
        AppReply<String> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(supplementaryInfoService.insertSupplementaryInfo(vo));
        return appReply;
    }

    /**
     * 更新追加单信息
     *
     * @param vo 追加单信息 vo实体
     * @return 无返回值
     */
    @PostMapping(value = "/updateById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 2, author = "pyj")
    @ApiOperation(value = "根据主键更新", notes = "根据主键更新表中信息 更新全部信息", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> updateByIdRecord(@ApiParam(value = "需要更新的追加单表", required = false) @RequestBody @Validated(UpdateValidatorGroup.class) BbpmSupplementaryInfoVo vo) {
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        supplementaryInfoService.updateSupplementaryInfo(vo);
        return appReply;
    }

    /**
     * 删除追加单信息
     *
     * @param supplementaryId 追加单id
     * @return 无返回值
     */
    @PostMapping(value = "/removeById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "pyj")
    @ApiOperation(value = "根据主键删除", notes = "根据主键删除表中信息 物理删除", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> removeByIdRecord(@ApiParam(value = "需要删除的追加单id", required = false) @RequestBody String supplementaryId) {
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        supplementaryInfoService.removeSupplementaryInfo(supplementaryId);
        return appReply;
    }

    /**
     * 查询追加单信息
     *
     * @param supplementaryId 追加单id
     * @return 追加单信息
     */
    @GetMapping(value = "/selectById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 4, author = "pyj")
    @ApiOperation(value = "根据主键查询", notes = "根据主键查询表中信息", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbpmSupplementaryInfoVo> selectByIdRecord(@ApiParam(value = "需要查询的追加单id", required = false) @RequestParam(required = false) String supplementaryId) {
        AppReply<BbpmSupplementaryInfoVo> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(supplementaryInfoService.selectSupplementaryInfo(supplementaryId));
        return appReply;
    }

    /**
     * 分页查询追加单列表
     *
     * @param vo 请求参数 vo实体
     * @return 追加单列表
     */
    @GetMapping(value = "/selectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 5, author = "pyj")
    @ApiOperation(value = "分页查询", notes = "分页查询", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmSupplementaryInfoPageResultVo>>> selectByPageRecord(BbpmSupplementaryInfoPageVo vo) {
        AppReply<PageResult<List<BbpmSupplementaryInfoPageResultVo>>> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(supplementaryInfoService.selectSupplementaryInfoPage(vo));
        return appReply;
    }

    /**
     * 提交
     *
     * @param supplementaryId 追加单id
     * @return 无返回值
     */
    @PostMapping(value = "/submit", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 6, author = "pyj")
    @ApiOperation(value = "提交", notes = "提交", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> submit(@ApiParam(value = "追加单id", required = false) @RequestBody String supplementaryId) {
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        supplementaryInfoService.submit(supplementaryId);
        return appReply;
    }

    /**
     * 撤回
     *
     * @param supplementaryId 追加单id
     * @return 无返回值
     */
    @PostMapping(value = "/cancel", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "pyj")
    @ApiOperation(value = "撤回", notes = "撤回", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> cancel(@ApiParam(value = "追加单id", required = false) @RequestBody String supplementaryId) {
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        supplementaryInfoService.cancel(supplementaryId);
        return appReply;
    }

    /**
     * 处理审核
     *
     * @param vo 审批 vo实体
     * @return 无返回值
     */
    @PostMapping(value = "/dealApprove", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8, author = "pyj")
    @ApiOperation(value = "处理审核", notes = "处理审核", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> dealApprove(@ApiParam(value = "审核实体", required = true) @RequestBody @Validated(UpdateValidatorGroup.class) BbpmApproveVo vo) {
        AppReply<Object> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        supplementaryInfoService.dealApprove(vo);
        return appReply;
    }

    /**
     * 分页查询追加单审批列表
     *
     * @param vo 请求参数 vo实体
     * @return 追加单审批列表
     */
    @GetMapping(value = "/selectApproveInfoPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "pyj")
    @ApiOperation(value = "分页查询", notes = "分页查询", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmSupplementaryInfoPageResultVo>>> selectApproveInfoPage(BbpmSupplementaryInfoPageVo vo) {
        AppReply<PageResult<List<BbpmSupplementaryInfoPageResultVo>>> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(supplementaryInfoService.selectApproveInfoPage(vo));
        return appReply;
    }

    /**
     * 追加单信息统计
     *
     * @return 追加单信息统计
     */
    @GetMapping(value = "/supplementaryInfoStatistic", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "pyj")
    @ApiOperation(value = "追加单信息统计", notes = "追加单信息统计", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbpmSupplementaryStatisticVo> supplementaryInfoStatistic() {
        AppReply<BbpmSupplementaryStatisticVo> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(supplementaryInfoService.supplementaryInfoStatistic());
        return appReply;
    }

    /**
     * 追加单审批信息统计
     *
     * @return 追加单审批信息统计
     */
    @GetMapping(value = "/approveInfoStatistic", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 11, author = "pyj")
    @ApiOperation(value = "追加单审批信息统计", notes = "追加单审批信息统计", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbpmSupplementaryStatisticVo> approveInfoStatistic() {
        AppReply<BbpmSupplementaryStatisticVo> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(supplementaryInfoService.approveInfoStatistic());
        return appReply;
    }

    /**
     * 获取操作记录列表
     *
     * @param supplementaryId 追加单id
     * @return 操作记录列表
     */
    @GetMapping(value = "/operateDetailInfo", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 12, author = "pyj")
    @ApiOperation(value = "操作记录", notes = "追加单审批信息统计", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<BbpmApproveDetailInfoVo>> operateDetailInfo(@ApiParam(value = "需要查询的追加单id", required = false) @RequestParam(required = false) String supplementaryId) {
        AppReply<List<BbpmApproveDetailInfoVo>> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(supplementaryInfoService.operateDetailInfo(supplementaryId));
        return appReply;
    }

    /**
     * 获取账单明细
     *
     * @param vo 请求参数 vo实体
     * @return 账单明细
     */
    @PostMapping(value = "/getPreviewBill", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 13, author = "pyj")
    @ApiOperation(value = "获取账单明细", notes = "获取账单明细", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Map<String, List<BbpmSupplementaryPreviewBillVo>>> getPreviewBill(@RequestBody BbpmSupplementaryInfoVo vo) {
        AppReply<Map<String, List<BbpmSupplementaryPreviewBillVo>>> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(supplementaryInfoService.getPreviewBill(vo));
        return appReply;
    }

    /**
     * 获取导出数据
     *
     * @return 导出数据
     */
    @GetMapping(value = "/getExportData", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 14, author = "pyj")
    @ApiOperation(value = "获取导出数据", notes = "获取导出数据", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<BbpmSupplementaryExportVo>> getExportData() {
        AppReply<List<BbpmSupplementaryExportVo>> appReply = new AppReply<>(AppReply.SUCCESS_CODE, AppReply.SUCCESS_MSG, null);
        appReply.setData(supplementaryInfoService.getExportData());
        return appReply;
    }
}
