package com.bonc.ioc.bzf.business.payment.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
@ApiModel(value="账单预览请求", description="账单预览请求")
@Data
public class PreviewBillsParamsVo implements java.io.Serializable{

    @ApiModelProperty(value = "新合同ID")
    private String contractId;

    @ApiModelProperty(value = "获取可抵扣账单列表")
    private Boolean queryDeductibleBillFlag;

    @ApiModelProperty(value = "变更账期类型 1合同起始日02当期第一天03下账期第一天")
    private String changeAccountingPeriodType;

    @ApiModelProperty(value = "缩减商铺Id")
    private List<String> cutShortRoomList;

    @ApiModelProperty(value = "合同起始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractBeginDate;

    @ApiModelProperty(value = "合同终止日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndDate;

    @ApiModelProperty(value = "合同计价周期 01 月 02 季 03 半年 04 年05 每2个月 06 每4个月")
    private String contractPricePeriod;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "项目编号")
    private String projectNo;

    @ApiModelProperty(value = "房源列表")
    private List<PreviewBillsParamsRoomVo> roomList;


}
