package com.bonc.ioc.bzf.business.payment.controller;

import com.bonc.ioc.bzf.business.payment.service.PreviewService;
import com.bonc.ioc.bzf.business.payment.vo.PayParmsVo;
import com.bonc.ioc.bzf.business.payment.vo.PayResultVo;
import com.bonc.ioc.bzf.business.payment.vo.PreviewBillsParamsVo;
import com.bonc.ioc.bzf.business.payment.vo.PreviewBillsResultVo;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.util.AppReply;
import io.swagger.annotations.*;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Validated
@RestController
@RequestMapping("/v2/priview")
@Api(tags = "预览模块")
@RefreshScope
public class PreviewController extends McpBaseController {

    @Resource
    private PreviewService previewService;
    @PostMapping(value = "/getPreviewBills", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 1, author = "姚春雨")
    @ApiOperation(value = "3.39. 商业合同账单预览接口", notes = "3.39. 商业合同账单预览接口")
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PreviewBillsResultVo> getPreviewBills(@Validated @RequestBody PreviewBillsParamsVo previewBillsParamsVo){
        return new AppReply<>(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,previewService.getPreviewBills(previewBillsParamsVo));
    }

}


