package com.bonc.ioc.bzf.business.payment.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.bonc.ioc.bzf.business.payment.vo.ContractChangePeriodDTO;
import com.bonc.ioc.bzf.business.payment.vo.RoomInfoVo;
import com.bonc.ioc.bzf.business.payment.config.NacosValueConfig;
import com.bonc.ioc.bzf.business.payment.enums.PaymentEnums;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.result.*;
import com.bonc.ioc.bzf.business.payment.result.create.*;
import com.bonc.ioc.bzf.business.payment.service.*;
import com.bonc.ioc.bzf.business.payment.utils.RedisDistributedId;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.context.SystemContextHolder;
import com.bonc.ioc.common.dict.session.McpDictSession;
import com.bonc.ioc.common.exception.McpException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BillCreateServiceImpl implements IBillCreateService {

    /**
     * service 本服务
     */
    @Resource
    private IBillCreateService baseService;

    @Resource
    private McpDictSession mcpDictSession;

    @Autowired
    RedisDistributedId redisDistributedId;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Value("${yecai.feign}")
    private boolean yecaiFeign;


    @Value("${yecai.url}")
    private String yecaiUrl;


    @Resource
    private BfipChargeFeignClient bfipChargeFeignClient;

    @Resource
    private RestTemplateUtil restTemplateUtil;

    @Resource
    private IBbpmMainLesseeExcelService iBbpmMainLesseeExcelService;

    @Resource
    private IBbsiRuleInfoService iBbsiRuleInfoService;

    @Resource
    private NacosValueConfig nacosValueConfig;


    /**
     * 合同生成账单  所有类型
     *
     * @param vo
     * @return
     */
    @Override
    public String billCreateAll(BbctPushInfoVo vo) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setTimeZone(TimeZone.getDefault());
        try {
            log.info("合同类型"+vo.getContractType()+",合同contractId"+vo.getContractId()+",签约中心传入的参数:" + objectMapper.writeValueAsString(vo));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        if (vo == null) {
            return null;
        }
        if (vo.getProductInfoList() == null || vo.getProductInfoList().size() == 0) {
            throw new McpException("产品信息列表不能为空");
        }
        if ((vo.getSignatoryInfoList() == null || vo.getSignatoryInfoList().size() == 0 ) && (vo.getProductInfoList().get(0) == null || vo.getProductInfoList().get(0).getSignatoryInfoList().size() == 0)) {
            throw new McpException("签约方信息列表不能为空");
        }
        switch (vo.getContractSourceType()) {
            case "7":
            case "8":
            case "11":
                return baseService.updateByContractAll(vo);
            case "22":
                return baseService.updateByContractAll(vo);
            case "10":
                return baseService.businessRelet(vo);
            case "3":
                if (PaymentEnums.TYPE_OF_CONTRACT_LOOSE.getCode().equals(vo.getContractType())) {
                    return baseService.looseRentRelet(vo);
                } else if (PaymentEnums.TYPE_OF_CONTRACT_SINGLERENT.getCode().equals(vo.getContractType())) {
                    return baseService.singleRelet(vo);
                } else if (PaymentEnums.TYPE_OF_CONTRACT_MANAGER.getCode().equals(vo.getContractType())) {
                    return baseService.agreementRelet(vo);
                } else {
                    throw new McpException("合同类型:contractType不匹配");
                }
            case "18":
                return baseService.storageRelet(vo);
            default:
                if (PaymentEnums.TYPE_OF_CONTRACT_LOOSE.getCode().equals(vo.getContractType())) {
                    return baseService.billCreate(vo);
                } else if (PaymentEnums.TYPE_OF_CONTRACT_SINGLERENT.getCode().equals(vo.getContractType())) {
                    return baseService.billReletCreate(vo);
                } else if (PaymentEnums.TYPE_OF_CONTRACT_MANAGER.getCode().equals(vo.getContractType())) {
                    return baseService.billAgreementCreate(vo);
                } else if (PaymentEnums.TYPE_OF_CONTRACT_BUSINESS.getCode().equals(vo.getContractType())) {
                    return baseService.businessCreate(vo);
                } else {
                    throw new McpException("合同类型:contractType不匹配");
                }
        }
    }


    /**
     * 对接工银    3.3合同签订生成账单接口
     * <p>
     * 联调写死字段：projectDistrict不能为空;projectLocation不能为空;houseNo不能为空;projectNo不能为空,  chargeSubjectAmount
     */
    @Override
    public String billCreate(BbctPushInfoVo vo) {
        //请求实体
        BankRequestVo<BillCreateParamsRequest> bankRequestVo = new BankRequestVo<>();

        //合同中心传递的--产品信息列表
        List<BbctPushProductInfoVo> productInfoList = vo.getProductInfoList();
        BbctPushProductInfoVo bbctPushProductInfoVo = getBbctPushProductInfo(productInfoList, 0);
        //合同中心传递的--签约方信息
        List<BbctPushSignatoryInfoVo> signatoryInfoList = vo.getSignatoryInfoList();
        //从产品信息列表里面取--兼容--
        if(signatoryInfoList == null || signatoryInfoList.size() == 0){
            signatoryInfoList = bbctPushProductInfoVo.getSignatoryInfoList();
        }
        BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(signatoryInfoList, 0);

        //3	chargeSubjectList	计费科目列表  租金和押金
        ChargeSubjectParamsRequest chargeSubjecParamsRequestOne = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_ONE.getCode());
        ChargeSubjectParamsRequest chargeSubjecParamsRequestTwo = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode());

        List<ChargeSubjectParamsRequest> chargeSubjectParamsRequestList = new ArrayList<>();
        List<BbctPushChargeSubjectVo> furnitureSubjectVos = bbctPushProductInfoVo.getChargeSubjectList().stream()
                .filter(element ->  PaymentEnums.CHARGE_SUBJECT_NO_THREE.getCode().equals(element.getChargeSubjectNo()))
                .collect(Collectors.toList());
        List<BbctPushChargeSubjectVo> carSubjectVos = bbctPushProductInfoVo.getChargeSubjectList().stream()
                .filter(element ->  PaymentEnums.CHARGE_SUBJECT_NO_FOUR.getCode().equals(element.getChargeSubjectNo()))
                .collect(Collectors.toList());
        List<BbctPushChargeSubjectVo> fiveSubjectVos = bbctPushProductInfoVo.getChargeSubjectList().stream()
                .filter(element ->  PaymentEnums.CHARGE_SUBJECT_NO_FIVE.getCode().equals(element.getChargeSubjectNo()))
                .collect(Collectors.toList());
        List<BbctPushChargeSubjectVo> fifteenSubjectVos = bbctPushProductInfoVo.getChargeSubjectList().stream()
                .filter(element ->  PaymentEnums.CHARGE_SUBJECT_NO_FIFTEEN.getCode().equals(element.getChargeSubjectNo()))
                .collect(Collectors.toList());

        ChargeSubjectParamsRequest chargeSubjecParamsRequestThree = null;
        ChargeSubjectParamsRequest chargeSubjecParamsRequestFour = null;
        ChargeSubjectParamsRequest chargeSubjecParamsRequestFive = null;
        ChargeSubjectParamsRequest chargeSubjecParamsRequestFifteen = null;
        if(furnitureSubjectVos!=null && furnitureSubjectVos.size() > 0){
            chargeSubjecParamsRequestThree = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_THREE.getCode());
        }
        if(carSubjectVos!=null && carSubjectVos.size() > 0){
            chargeSubjecParamsRequestFour = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_FOUR.getCode());
        }
        if(fiveSubjectVos!=null && fiveSubjectVos.size() > 0){
            chargeSubjecParamsRequestFive = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_FIVE.getCode());
        }
        if(fifteenSubjectVos!=null && fifteenSubjectVos.size() > 0){
            chargeSubjecParamsRequestFifteen = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_FIFTEEN.getCode());
        }

        if(chargeSubjecParamsRequestOne != null && StringUtils.isNotBlank(chargeSubjecParamsRequestOne.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestOne);
        }
        if(chargeSubjecParamsRequestTwo != null && StringUtils.isNotBlank(chargeSubjecParamsRequestTwo.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestTwo);
        }
        if(chargeSubjecParamsRequestThree != null && StringUtils.isNotBlank(chargeSubjecParamsRequestThree.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestThree);
        }
        if(chargeSubjecParamsRequestFour != null && StringUtils.isNotBlank(chargeSubjecParamsRequestFour.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestFour);
        }
        if(chargeSubjecParamsRequestFive != null && StringUtils.isNotBlank(chargeSubjecParamsRequestFive.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestFive);
        }
        if(chargeSubjecParamsRequestFifteen != null && StringUtils.isNotBlank(chargeSubjecParamsRequestFifteen.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestFifteen);
        }

        //最终发送对象
        BillCreateParamsRequest billCreateParamsRequest = BillCreateParamsRequest.builder()
                .historyData(vo.getHistoryData())

                .originalContractId(vo.getParentContractCode())
                .reletDeductionFlag(vo.getReletDeductionFlag())

                .chargeSubjectList(chargeSubjectParamsRequestList)
//                .chargeSubjecParamsRequest(chargeSubjecParamsRequestOne)
//                .chargeSubjecParamsRequest(chargeSubjecParamsRequestTwo)
                //租户
                .tenantBankName(bbctPushSignatoryInfoVo.getTenantBankName())
                .tenantBankCode(bbctPushSignatoryInfoVo.getTenantBankCode())
                .tenantBankAccountName(bbctPushSignatoryInfoVo.getTenantBankAccountName())
                .tenantBankAccountNo(bbctPushSignatoryInfoVo.getTenantBankAccountNo())
                .withholding(bbctPushSignatoryInfoVo.getWithholding())
                .withholdingSummary(PaymentEnums.WITHHOLDING_YES.getCode().equals(bbctPushSignatoryInfoVo.getWithholding()) ? "房租" : null)
                .withholdingRemark(PaymentEnums.WITHHOLDING_YES.getCode().equals(bbctPushSignatoryInfoVo.getWithholding()) ? "3" : null)
                .agreementNo(StringUtils.isNotBlank(bbctPushSignatoryInfoVo.getAgreementNo())? bbctPushSignatoryInfoVo.getAgreementNo() : "0000")
                .tenantName(bbctPushSignatoryInfoVo.getTenantName())
                .tenantMobile(bbctPushSignatoryInfoVo.getTenantMobile())
                .tenantId(bbctPushSignatoryInfoVo.getTenantId())
                .tenantIDType(bbctPushSignatoryInfoVo.getTenantIDType())
                .idNumber(bbctPushSignatoryInfoVo.getIdNumber())
                .mailUrl(bbctPushSignatoryInfoVo.getMailUrl())
                .publicRecordNo(bbctPushSignatoryInfoVo.getPublicRecordNo())
                .tenantCustomerNo(bbctPushSignatoryInfoVo.getTenantCustomerNo())
                .tenantSupplierNo(bbctPushSignatoryInfoVo.getTenantSupplierNo())
                .tenantSupplierName(bbctPushSignatoryInfoVo.getTenantSupplierName())
                //项目
                .projectId(bbctPushProductInfoVo.getProjectId())
                .projectNo(StringUtils.isNotBlank(bbctPushProductInfoVo.getProjectNo()) ? bbctPushProductInfoVo.getProjectNo() : bbctPushProductInfoVo.getProjectId())
                .projectName(bbctPushProductInfoVo.getProjectName())
                .projectShortName(bbctPushProductInfoVo.getProjectShortName())
                .operateEntityType(bbctPushProductInfoVo.getOperateEntityType())
                .operateEntityName(bbctPushProductInfoVo.getOperateEntityName())
                .operateUnitBusinessNo(bbctPushProductInfoVo.getOperateUnitBusinessNo())
                .operateUnitNo(bbctPushProductInfoVo.getOperateUnitNo())
                .operateUnitName(bbctPushProductInfoVo.getOperateUnitName())
                .projectAreaBusinessNo(bbctPushProductInfoVo.getProjectAreaBusinessNo())
                .projectAreaNo(bbctPushProductInfoVo.getProjectAreaNo())
                .projectAreaName(bbctPushProductInfoVo.getProjectAreaName())
                .projectFormat(bbctPushProductInfoVo.getProjectFormat())
                .projectEstate(bbctPushProductInfoVo.getProjectEstate())
                .projectDistrict("99999")
                .projectLocation("99999")
                //房屋
                .houseId(bbctPushProductInfoVo.getHouseId())
                .houseNo(StringUtils.isNotBlank(bbctPushProductInfoVo.getHouseNo()) ? bbctPushProductInfoVo.getHouseNo() : bbctPushProductInfoVo.getHouseId())
                .houseName(bbctPushProductInfoVo.getHouseName())
                .buildingNo(bbctPushProductInfoVo.getBuildingNo())
                .unitNo(bbctPushProductInfoVo.getUnitNo())
                .floorNo((PaymentEnums.CONTRACTSOURCETYPE_CAR.getCode().equals(vo.getContractSourceType())
                        || PaymentEnums.CONTRACTSOURCETYPE_STORAGE.getCode().equals(vo.getContractSourceType()))
                        ? bbctPushProductInfoVo.getCurrentFloorNo() : bbctPushProductInfoVo.getCurrentFloorNo() + "/" + bbctPushProductInfoVo.getTotalFloorNo())
                .roomNo(bbctPushProductInfoVo.getRoomNo())
                .houseType(bbctPushProductInfoVo.getHouseType())
                .houseOrientation(bbctPushProductInfoVo.getHouseOrientation())
                //合同
                .contractId(vo.getContractId())
                .oldContractId(vo.getOldContractId())
                .contractClassification(vo.getContractClassification())
                .contractType(vo.getContractType())
                .parkContractType(vo.getParkContractType())
                .contractStatus(vo.getContractStatus())
                .contractBeginDate(vo.getContractBeginDate())
                .contractEndDate(vo.getContractEndDate())
                .contractSignTime(vo.getContractSignTime())
                .contractCommencementDate(vo.getContractCommencementDate())
                .contractPriceUnit(vo.getContractPriceUnit())
                .contractPricePeriod(vo.getContractPricePeriod())
                .contractArea(vo.getContractArea())
                .roomType(bbctPushProductInfoVo.getRoomType())
                .approver(vo.getApprover())
                .approveTime(vo.getApproveTime())
                .parkPropertyType(bbctPushProductInfoVo.getParkPropertyType())
                .carLocation(bbctPushProductInfoVo.getCarLocation())

                //reits
                .beforeContractId(vo.getBeforeContractId())
                .beforeProjectId(vo.getBeforeProjectId())
                .sceneType(vo.getSceneType())

                //区域
//                .houseArea(bbctPushProductInfoVo.getHouseArea())

                .noCompleteType(vo.getNoCompleteType())

                .build();

        //家具
        List<BbctFurnitureRentalVo> furnitureRentalList = bbctPushProductInfoVo.getFurnitureRentalList();
        List<FurnitureRentalParamsRequest> furnitureRentalParamsRequestList = new ArrayList<>();
        if (furnitureRentalList != null && furnitureRentalList.size() > 0) {
            for(BbctFurnitureRentalVo bbctFurnitureRentalVo : furnitureRentalList){
                FurnitureRentalParamsRequest furnitureRentalParamsRequest = FurnitureRentalParamsRequest.builder()
                        .furnitureId(bbctFurnitureRentalVo.getFurnitureId())
                        .furnitureName(bbctFurnitureRentalVo.getFurnitureName())
                        .furnitureRental(bbctFurnitureRentalVo.getFurnitureRental())
                        .build();
                furnitureRentalParamsRequestList.add(furnitureRentalParamsRequest);
            }
        } else {
            furnitureRentalParamsRequestList = null;
        }
        billCreateParamsRequest.setFurnitureRentalList(furnitureRentalParamsRequestList);

        bankRequestVo.setData(billCreateParamsRequest);

        ChargeRespondVo result;
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getDefault());
        if (yecaiFeign) {
            log.info("===================================================================================================================");
            try {
                log.info("合同contractId"+vo.getContractId()+",3.3合同签订生成账单接口, 请求参数(工银feign)：" + mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            result = bfipChargeFeignClient.billCreate(bankRequestVo);
        } else {
            String url = yecaiUrl + "/charge/v1/bill/create";
            try {
                result = restTemplateUtil.postJsonStringByVo(url, mapper.writeValueAsString(bankRequestVo));
                //3.3合同签订生成账单接口返回结果:{"busiCode":"","code":"00000","message":"合同签订生成账单成功"}
                //log.info("3.3合同签订生成账单接口返回结果:"+JSONObject.toJSONString(result));
            } catch (JsonProcessingException e) {
                throw new McpException("格式转化错误");
            }
        }
        log.info("合同contractId"+vo.getContractId()+",3.3合同签订生成账单接口,工银返回:"+JSONObject.toJSONString(result));

        if (!"00000".equals(result.getCode())) {
            throw new McpException("*提示:" + result.getMessage());
        }

        return result.getCode();
    }

    /**
     * 对接工银 3.32散租续签生成账单
     */
    @Override
    public String looseRentRelet(BbctPushInfoVo vo) {
        //请求实体
        BankRequestVo<BillLooseRentReletCreateParamsRequest> bankRequestVo = new BankRequestVo<>();

        //合同中心传递的--产品信息列表
        List<BbctPushProductInfoVo> productInfoList = vo.getProductInfoList();
        BbctPushProductInfoVo bbctPushProductInfoVo = getBbctPushProductInfo(productInfoList, 0);
        //合同中心传递的--签约方信息
        List<BbctPushSignatoryInfoVo> signatoryInfoList = vo.getSignatoryInfoList();
        //从产品信息列表里面取--兼容--
        if(signatoryInfoList == null || signatoryInfoList.size() == 0){
            signatoryInfoList = bbctPushProductInfoVo.getSignatoryInfoList();
        }
        BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(signatoryInfoList, 0);

        //3	chargeSubjectList	计费科目列表  租金和押金
        ChargeSubjectParamsRequest chargeSubjecParamsRequestOne = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_ONE.getCode());
        ChargeSubjectParamsRequest chargeSubjecParamsRequestTwo = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode());
        List<ChargeSubjectParamsRequest> chargeSubjectParamsRequestList = new ArrayList<>();
        if(chargeSubjecParamsRequestOne != null && StringUtils.isNotBlank(chargeSubjecParamsRequestOne.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestOne);
        }
        if(chargeSubjecParamsRequestTwo != null && StringUtils.isNotBlank(chargeSubjecParamsRequestTwo.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestTwo);
        }

        //最终发送对象
        BillLooseRentReletCreateParamsRequest billLooseRentReletCreateParamsRequest = BillLooseRentReletCreateParamsRequest.builder()
                .originalContractId(vo.getParentContractCode())
                .rentStandardType(bbctPushProductInfoVo.getRentStandardType())
                .qualificationPassDate(vo.getQualificationPassDate())

                .historyData(vo.getHistoryData())

                .chargeSubjectList(chargeSubjectParamsRequestList)
//                .chargeSubjecParamsRequest(chargeSubjecParamsRequestOne)
//                .chargeSubjecParamsRequest(chargeSubjecParamsRequestTwo)
                //租户
                .tenantBankName(bbctPushSignatoryInfoVo.getTenantBankName())
                .tenantBankCode(bbctPushSignatoryInfoVo.getTenantBankCode())
                .tenantBankAccountName(bbctPushSignatoryInfoVo.getTenantBankAccountName())
                .tenantBankAccountNo(bbctPushSignatoryInfoVo.getTenantBankAccountNo())
                .withholding(bbctPushSignatoryInfoVo.getWithholding())
                .withholdingSummary(PaymentEnums.WITHHOLDING_YES.getCode().equals(bbctPushSignatoryInfoVo.getWithholding()) ? "房租" : null)
                .withholdingRemark(PaymentEnums.WITHHOLDING_YES.getCode().equals(bbctPushSignatoryInfoVo.getWithholding()) ? "3" : null)
                .agreementNo(StringUtils.isNotBlank(bbctPushSignatoryInfoVo.getAgreementNo()) ? bbctPushSignatoryInfoVo.getAgreementNo() : "0000")
                .tenantName(bbctPushSignatoryInfoVo.getTenantName())
                .tenantMobile(bbctPushSignatoryInfoVo.getTenantMobile())
                .tenantId(bbctPushSignatoryInfoVo.getTenantId())
                .tenantIDType(bbctPushSignatoryInfoVo.getTenantIDType())
                .idNumber(bbctPushSignatoryInfoVo.getIdNumber())
                .mailUrl(bbctPushSignatoryInfoVo.getMailUrl())
                .publicRecordNo(bbctPushSignatoryInfoVo.getPublicRecordNo())
                .tenantCustomerNo(bbctPushSignatoryInfoVo.getTenantCustomerNo())
                .tenantSupplierNo(bbctPushSignatoryInfoVo.getTenantSupplierNo())
                .tenantSupplierName(bbctPushSignatoryInfoVo.getTenantSupplierName())
                //项目
                .projectId(bbctPushProductInfoVo.getProjectId())
                .projectNo(StringUtils.isNotBlank(bbctPushProductInfoVo.getProjectNo()) ? bbctPushProductInfoVo.getProjectNo() : bbctPushProductInfoVo.getProjectId())
                .projectName(bbctPushProductInfoVo.getProjectName())
                .projectShortName(bbctPushProductInfoVo.getProjectShortName())
                .operateEntityType(bbctPushProductInfoVo.getOperateEntityType())
                .operateEntityName(bbctPushProductInfoVo.getOperateEntityName())
                .operateUnitBusinessNo(bbctPushProductInfoVo.getOperateUnitBusinessNo())
                .operateUnitNo(bbctPushProductInfoVo.getOperateUnitNo())
                .operateUnitName(bbctPushProductInfoVo.getOperateUnitName())
                .projectAreaBusinessNo(bbctPushProductInfoVo.getProjectAreaBusinessNo())
                .projectAreaNo(bbctPushProductInfoVo.getProjectAreaNo())
                .projectAreaName(bbctPushProductInfoVo.getProjectAreaName())
                .projectFormat(bbctPushProductInfoVo.getProjectFormat())
                .projectEstate(bbctPushProductInfoVo.getProjectEstate())
                .projectDistrict("99999")
                .projectLocation("99999")
                //房屋
                .houseId(bbctPushProductInfoVo.getHouseId())
                .houseNo(StringUtils.isNotBlank(bbctPushProductInfoVo.getHouseNo()) ? bbctPushProductInfoVo.getHouseNo() : bbctPushProductInfoVo.getHouseId())
                .houseName(bbctPushProductInfoVo.getHouseName())
                .buildingNo(bbctPushProductInfoVo.getBuildingNo())
                .unitNo(bbctPushProductInfoVo.getUnitNo())
                .floorNo(bbctPushProductInfoVo.getCurrentFloorNo() + "/" + bbctPushProductInfoVo.getTotalFloorNo())
                .roomNo(bbctPushProductInfoVo.getRoomNo())
                .houseType(bbctPushProductInfoVo.getHouseType())
                .houseOrientation(bbctPushProductInfoVo.getHouseOrientation())
                //合同
                .contractId(vo.getContractId())
                .oldContractId(vo.getOldContractId())
                .contractClassification(vo.getContractClassification())
                .contractType(vo.getContractType())
                .contractStatus(vo.getContractStatus())
                .contractBeginDate(vo.getContractBeginDate())
                .contractEndDate(vo.getContractEndDate())
                .contractSignTime(vo.getContractSignTime())
                .contractCommencementDate(vo.getContractCommencementDate())
                .contractPriceUnit(vo.getContractPriceUnit())
                .contractPricePeriod(vo.getContractPricePeriod())
                .contractArea(vo.getContractArea())
                .roomType(bbctPushProductInfoVo.getRoomType())
                .approver(vo.getApprover())
                .approveTime(vo.getApproveTime())

                //区域
//                .houseArea(bbctPushProductInfoVo.getHouseArea())

                .build();

        billLooseRentReletCreateParamsRequest.setFurnitureRentalList(null);

        bankRequestVo.setData(billLooseRentReletCreateParamsRequest);

        ChargeRespondVo result;
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getDefault());
        if (yecaiFeign) {
            log.info("===================================================================================================================");
            try {
                log.info("合同contractId"+vo.getContractId()+",3.32散租续签生成账单接口, 请求参数(工银feign)：" + mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            result = bfipChargeFeignClient.looseRentRelet(bankRequestVo);
        } else {
            String url = yecaiUrl + "/charge/v1/bill/looseRentRelet";
            try {
                result = restTemplateUtil.postJsonStringByVo(url, mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                throw new McpException("格式转化错误");
            }
        }
        log.info("合同contractId"+vo.getContractId()+",3.32散租续签生成账单接口,工银返回:"+JSONObject.toJSONString(result));

        if (!"00000".equals(result.getCode())) {
            throw new McpException("*提示:" + result.getMessage());
        }

        return result.getCode();
    }

    /**
     * 对接工银 3.26. 趸租大合同生成账单接口
     */
    @Override
    public String billReletCreate(BbctPushInfoVo vo) {
        boolean isNew = true;
        //请求实体
        BankRequestVo<BillReletCreateParamsRequest> bankRequestVo = new BankRequestVo<>();
        //合同中心传递的--签约方信息
        List<BbctPushSignatoryInfoVo> signatoryInfoList = vo.getSignatoryInfoList();
        BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo = new BbctPushSignatoryInfoVo();
        //--兼容--
        if(signatoryInfoList != null && signatoryInfoList.size() > 0){
            bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(signatoryInfoList, 0);
            isNew = false;
        }

        List<ProjectRequest> projectList = new ArrayList<>();
        //合同中心传递的--产品信息列表
        List<BbctPushProductInfoVo> productInfoList = vo.getProductInfoList();
        //得到项目分组
        Map<String, List<BbctPushProductInfoVo>> projectsByProjectIdMap = productInfoList.stream()
                .collect(Collectors.groupingBy(BbctPushProductInfoVo::getProjectId));
        //同一个项目相关
        for (Map.Entry<String, List<BbctPushProductInfoVo>> entry : projectsByProjectIdMap.entrySet()) {
            String projectId = entry.getKey();
            List<BbctPushProductInfoVo> productInfoVos = entry.getValue();
            BbctPushProductInfoVo project = productInfoVos.get(0);

            List<RoomRequest> roomList = new ArrayList<>();
            for (int m=0;m<productInfoVos.size();m++) {
                BbctPushProductInfoVo bbctPushProductInfoVo = productInfoVos.get(m);

                //从产品信息列表里面取--兼容--
                if(isNew){
                    signatoryInfoList =  bbctPushProductInfoVo.getSignatoryInfoList().stream()
                            .filter(element -> PaymentEnums.SIGNATORYTYPE_ENTERPRISE.getCode().equals(element.getSignatoryType()))
                            .collect(Collectors.toList());

                    bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(signatoryInfoList, 0);
                }

                //家具
                List<BbctFurnitureRentalVo> furnitureRentalList = bbctPushProductInfoVo.getFurnitureRentalList();
                List<FurnitureRentalParamsRequest> furnitureRentalParamsRequestList = new ArrayList<>();
                if (furnitureRentalList != null && furnitureRentalList.size() > 0) {
                    for(BbctFurnitureRentalVo bbctFurnitureRentalVo : furnitureRentalList){
                        FurnitureRentalParamsRequest furnitureRentalParamsRequest = FurnitureRentalParamsRequest.builder()
                                .furnitureId(bbctFurnitureRentalVo.getFurnitureId())
                                .furnitureName(bbctFurnitureRentalVo.getFurnitureName())
                                .furnitureRental(bbctFurnitureRentalVo.getFurnitureRental())
                                .build();
                        furnitureRentalParamsRequestList.add(furnitureRentalParamsRequest);
                    }
                } else {
                    furnitureRentalParamsRequestList = null;
                }

                //3	chargeSubjectList	计费科目列表  租金和押金
                ChargeSubjectParamsRequest chargeSubjecParamsRequestOne = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_ONE.getCode());
                ChargeSubjectParamsRequest chargeSubjecParamsRequestTwo = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode());

                List<ChargeSubjectParamsRequest> chargeSubjectParamsRequestList = new ArrayList<>();
                List<BbctPushChargeSubjectVo> furnitureSubjectVos = bbctPushProductInfoVo.getChargeSubjectList().stream()
                        .filter(element ->  PaymentEnums.CHARGE_SUBJECT_NO_THREE.getCode().equals(element.getChargeSubjectNo()))
                        .collect(Collectors.toList());
                List<BbctPushChargeSubjectVo> carSubjectVos = bbctPushProductInfoVo.getChargeSubjectList().stream()
                        .filter(element ->  PaymentEnums.CHARGE_SUBJECT_NO_FOUR.getCode().equals(element.getChargeSubjectNo()))
                        .collect(Collectors.toList());
                List<BbctPushChargeSubjectVo> fifteenSubjectVos = bbctPushProductInfoVo.getChargeSubjectList().stream()
                        .filter(element ->  PaymentEnums.CHARGE_SUBJECT_NO_FIFTEEN.getCode().equals(element.getChargeSubjectNo()))
                        .collect(Collectors.toList());
                ChargeSubjectParamsRequest chargeSubjecParamsRequestThree = null;
                ChargeSubjectParamsRequest chargeSubjecParamsRequestFour = null;
                ChargeSubjectParamsRequest chargeSubjecParamsRequestFifteen = null;
                if(furnitureSubjectVos!=null && furnitureSubjectVos.size() > 0){
                    chargeSubjecParamsRequestThree = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_THREE.getCode());
                }
                if(carSubjectVos!=null && carSubjectVos.size() > 0){
                    chargeSubjecParamsRequestFour = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_FOUR.getCode());
                }
                if(fifteenSubjectVos!=null && fifteenSubjectVos.size() > 0){
                    chargeSubjecParamsRequestFifteen = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_FIFTEEN.getCode());
                }
                if(chargeSubjecParamsRequestOne != null && StringUtils.isNotBlank(chargeSubjecParamsRequestOne.getChargeSubjectNo())){
                    chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestOne);
                }
                if(chargeSubjecParamsRequestTwo != null && StringUtils.isNotBlank(chargeSubjecParamsRequestTwo.getChargeSubjectNo())){
                    chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestTwo);
                }
                if(chargeSubjecParamsRequestThree != null && StringUtils.isNotBlank(chargeSubjecParamsRequestThree.getChargeSubjectNo())){
                    chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestThree);
                }
                if(chargeSubjecParamsRequestFour != null && StringUtils.isNotBlank(chargeSubjecParamsRequestFour.getChargeSubjectNo())){
                    chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestFour);
                }
                if(chargeSubjecParamsRequestFifteen != null && StringUtils.isNotBlank(chargeSubjecParamsRequestFifteen.getChargeSubjectNo())){
                    chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestFifteen);
                }
                RoomRequest roomRequest = RoomRequest.builder()
                        //计费科目
                        .chargeSubjectList(chargeSubjectParamsRequestList)
//                        .chargeSubjecParamsRequest(chargeSubjecParamsRequestOne)
//                        .chargeSubjecParamsRequest(chargeSubjecParamsRequestTwo)
//                        .furnitureRentalList(furnitureRentalParamsRequestList)
                        //房屋
                        .houseId(bbctPushProductInfoVo.getHouseId())
                        .houseNo(StringUtils.isNotBlank(bbctPushProductInfoVo.getHouseNo()) ? bbctPushProductInfoVo.getHouseNo() : bbctPushProductInfoVo.getHouseId())
                        .houseName(bbctPushProductInfoVo.getHouseName())
                        .buildingNo(bbctPushProductInfoVo.getBuildingNo())
                        .unitNo(bbctPushProductInfoVo.getUnitNo())
                        .floorNo(PaymentEnums.CONTRACTSOURCETYPE_CAR.getCode().equals(vo.getContractSourceType())?bbctPushProductInfoVo.getCurrentFloorNo():bbctPushProductInfoVo.getCurrentFloorNo() + "/" + bbctPushProductInfoVo.getTotalFloorNo())
                        .roomNo(bbctPushProductInfoVo.getRoomNo())
                        .houseType(bbctPushProductInfoVo.getHouseType())
                        .houseOrientation(bbctPushProductInfoVo.getHouseOrientation())
                        .roomType(bbctPushProductInfoVo.getRoomType())
                        .parkPropertyType(bbctPushProductInfoVo.getParkPropertyType())
                        .parkLocation(bbctPushProductInfoVo.getCarLocation())
                        //区域
//                        .houseArea(bbctPushProductInfoVo.getHouseArea())
                        .build();
                roomRequest.setFurnitureRentalList(furnitureRentalParamsRequestList);
                roomList.add(roomRequest);
            }
            ProjectRequest projectRequest = ProjectRequest.builder()
                    //项目
                    .projectId(projectId)
                    .projectNo(StringUtils.isNotBlank(project.getProjectNo()) ? project.getProjectNo() : project.getProjectId())
                    .projectName(project.getProjectName())
                    .projectShortName(project.getProjectShortName())
                    .operateEntityType(project.getOperateEntityType())
                    .operateEntityName(project.getOperateEntityName())
                    .operateUnitBusinessNo(project.getOperateUnitBusinessNo())
                    .operateUnitNo(project.getOperateUnitNo())
                    .operateUnitName(project.getOperateUnitName())
                    .projectAreaBusinessNo(project.getProjectAreaBusinessNo())
                    .projectAreaNo(project.getProjectAreaNo())
                    .projectAreaName(project.getProjectAreaName())
                    .projectFormat(project.getProjectFormat())
                    .projectEstate(project.getProjectEstate())
                    .projectDistrict("99999")
                    .projectLocation("99999")
                    //房屋
                    .roomList(roomList)
                    .build();
            projectList.add(projectRequest);
        }

        //最终发送对象
        BillReletCreateParamsRequest billReletCreateParamsRequest = BillReletCreateParamsRequest.builder()
                .historyData(vo.getHistoryData())
                //企业
                .companyId(bbctPushSignatoryInfoVo.getCompanyId())
                .companyIDType(bbctPushSignatoryInfoVo.getCompanyIDType())
                .socialCreditCode(bbctPushSignatoryInfoVo.getSocialCreditCode())
                .companyName(bbctPushSignatoryInfoVo.getCompanyName())
                .companyCustomerNo(bbctPushSignatoryInfoVo.getCompanyCustomerNo())
                .companySupplierNo(bbctPushSignatoryInfoVo.getCompanySupplierNo())
                .companySupplierName(bbctPushSignatoryInfoVo.getCompanySupplierName())
                .authorizedAgent(bbctPushSignatoryInfoVo.getAuthorizedAgent())
                .authorizedAgentMobile(bbctPushSignatoryInfoVo.getAuthorizedAgentMobile())

                //合同
                .contractId(vo.getContractId())
                .contractClassification(vo.getContractClassification())
                .contractType(vo.getContractType())
                .parkContractType(vo.getParkContractType())
                .contractStatus(vo.getContractStatus())
                .contractBeginDate(vo.getContractBeginDate())
                .contractEndDate(vo.getContractEndDate())
                .contractSignTime(vo.getContractSignTime())
                .contractCommencementDate(vo.getContractCommencementDate())
                .contractPriceUnit(vo.getContractPriceUnit())
                .contractPricePeriod(vo.getContractPricePeriod())
                .contractArea(vo.getContractArea())
                .approver(vo.getApprover())
                .approveTime(vo.getApproveTime())
                //项目相关
                .projectList(projectList)
                //企业银行
                .publicRecordNo(bbctPushSignatoryInfoVo.getPublicRecordNo())
                .companyBankName(bbctPushSignatoryInfoVo.getCompanyBankName())
                .companyBankCode(bbctPushSignatoryInfoVo.getCompanyBankCode())
                .companyBankBranchName(bbctPushSignatoryInfoVo.getCompanyBankBranchName())
                .companyBankBranchCode(bbctPushSignatoryInfoVo.getCompanyBankBranchCode())
                .companyBankAccountName(bbctPushSignatoryInfoVo.getCompanyBankAccountName())
                .companyBankAccountNo(bbctPushSignatoryInfoVo.getCompanyBankAccountNo())
                .companyTaxNo(bbctPushSignatoryInfoVo.getCompanyTaxNo())

                //reits
                .beforeContractId(vo.getBeforeContractId())
                .sceneType(vo.getSceneType())

                .noCompleteType(vo.getNoCompleteType())

                .build();

        //reits
        if(vo.getProjectReitsList()!=null && vo.getProjectReitsList().size()>0){
            billReletCreateParamsRequest.setProjectReitsList(vo.getProjectReitsList());
        }

        bankRequestVo.setData(billReletCreateParamsRequest);

        ChargeRespondVo result;
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getDefault());
        log.info("合同contractId"+vo.getContractId()+",3.26趸租大合同生成账单接口"+JSONObject.toJSONString(bankRequestVo, SerializerFeature.WriteMapNullValue));
        if (yecaiFeign) {
            log.info("===================================================================================================================");
            try {
                log.info("合同contractId"+vo.getContractId()+",3.26趸租大合同生成账单接口, 请求参数(工银feign)：" + mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            result = bfipChargeFeignClient.billReletCreate(bankRequestVo);
        } else {
            String url = yecaiUrl + "/charge/v1/bill/relet/create";
            try {
                result = restTemplateUtil.postJsonStringByVo(url, mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                throw new McpException("格式转化错误");
            }
        }
        log.info("合同contractId"+vo.getContractId()+",3.26趸租大合同生成账单接口,工银返回:"+JSONObject.toJSONString(result));

        if (!"00000".equals(result.getCode())) {
            throw new McpException("*提示:" + result.getMessage());
        }

        return result.getCode();
    }

    /**
     * 对接工银 3.28 趸租大合同续租生成账单接口
     */
    @Override
    public String singleRelet(BbctPushInfoVo vo) {
        boolean isNew = true;
        //请求实体
        BankRequestVo<BillSingleReletParamsRequest> bankRequestVo = new BankRequestVo<>();
        //合同中心传递的--签约方信息
        List<BbctPushSignatoryInfoVo> signatoryInfoList = vo.getSignatoryInfoList();
        BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo = new BbctPushSignatoryInfoVo();
        //--兼容--
        if(signatoryInfoList != null && signatoryInfoList.size() > 0){
            bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(signatoryInfoList, 0);
            isNew = false;
        }

        List<ProjectRequest> projectList = new ArrayList<>();
        //合同中心传递的--产品信息列表
        List<BbctPushProductInfoVo> productInfoList = vo.getProductInfoList();
        //得到项目分组
        Map<String, List<BbctPushProductInfoVo>> projectsByProjectIdMap = productInfoList.stream()
                .collect(Collectors.groupingBy(BbctPushProductInfoVo::getProjectId));
        //同一个项目相关
        for (Map.Entry<String, List<BbctPushProductInfoVo>> entry : projectsByProjectIdMap.entrySet()) {
            String projectId = entry.getKey();
            List<BbctPushProductInfoVo> productInfoVos = entry.getValue();
            BbctPushProductInfoVo project = productInfoVos.get(0);
            List<RoomRequest> roomList = new ArrayList<>();
            for (int m=0;m<productInfoVos.size();m++) {
                BbctPushProductInfoVo bbctPushProductInfoVo = productInfoVos.get(m);

                //从产品信息列表里面取--兼容--
                if(isNew){
                    signatoryInfoList =  bbctPushProductInfoVo.getSignatoryInfoList().stream()
                            .filter(element -> PaymentEnums.SIGNATORYTYPE_ENTERPRISE.getCode().equals(element.getSignatoryType()))
                            .collect(Collectors.toList());
                }
                bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(signatoryInfoList, 0);


                //3	chargeSubjectList	计费科目列表  租金和押金
                ChargeSubjectParamsRequest chargeSubjecParamsRequestOne = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_ONE.getCode());
                ChargeSubjectParamsRequest chargeSubjecParamsRequestTwo = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode());
                List<ChargeSubjectParamsRequest> chargeSubjectParamsRequestList = new ArrayList<>();
                if(chargeSubjecParamsRequestOne != null && StringUtils.isNotBlank(chargeSubjecParamsRequestOne.getChargeSubjectNo())){
                    chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestOne);
                }
                if(chargeSubjecParamsRequestTwo != null && StringUtils.isNotBlank(chargeSubjecParamsRequestTwo.getChargeSubjectNo())){
                    chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestTwo);
                }

                RoomRequest roomRequest = RoomRequest.builder()
                        //计费科目
                        .chargeSubjectList(chargeSubjectParamsRequestList)
//                        .chargeSubjecParamsRequest(chargeSubjecParamsRequestOne)
//                        .chargeSubjecParamsRequest(chargeSubjecParamsRequestTwo)
                        //房屋
                        .houseId(bbctPushProductInfoVo.getHouseId())
                        .houseNo(StringUtils.isNotBlank(bbctPushProductInfoVo.getHouseNo()) ? bbctPushProductInfoVo.getHouseNo() : bbctPushProductInfoVo.getHouseId())
                        .houseName(bbctPushProductInfoVo.getHouseName())
//                        .houseNumberNo()
//                        .houseNumberName()
                        .buildingNo(bbctPushProductInfoVo.getBuildingNo())
                        .unitNo(bbctPushProductInfoVo.getUnitNo())
                        .floorNo(bbctPushProductInfoVo.getCurrentFloorNo() + "/" + bbctPushProductInfoVo.getTotalFloorNo())
                        .roomNo(bbctPushProductInfoVo.getRoomNo())
                        .houseType(bbctPushProductInfoVo.getHouseType())
                        .houseOrientation(bbctPushProductInfoVo.getHouseOrientation())
                        .roomType(bbctPushProductInfoVo.getRoomType())
                        //区域
//                        .houseArea(bbctPushProductInfoVo.getHouseArea())
                        .build();
                roomRequest.setFurnitureRentalList(null);
                roomList.add(roomRequest);
            }
            ProjectRequest projectRequest = ProjectRequest.builder()
                    //项目
                    .projectId(projectId)
                    .projectNo(StringUtils.isNotBlank(project.getProjectNo()) ? project.getProjectNo() : project.getProjectId())
                    .projectName(project.getProjectName())
                    .projectShortName(project.getProjectShortName())
                    .operateEntityType(project.getOperateEntityType())
                    .operateEntityName(project.getOperateEntityName())
                    .operateUnitBusinessNo(project.getOperateUnitBusinessNo())
                    .operateUnitNo(project.getOperateUnitNo())
                    .operateUnitName(project.getOperateUnitName())
                    .projectAreaBusinessNo(project.getProjectAreaBusinessNo())
                    .projectAreaNo(project.getProjectAreaNo())
                    .projectAreaName(project.getProjectAreaName())
                    .projectFormat(project.getProjectFormat())
                    .projectEstate(project.getProjectEstate())
                    .projectDistrict("99999")
                    .projectLocation("99999")
                    //房屋
                    .roomList(roomList)
                    .build();
            projectList.add(projectRequest);
        }


        //最终发送对象
        BillSingleReletParamsRequest billSingleReletParamsRequest = BillSingleReletParamsRequest.builder()
                .originalContractId(vo.getParentContractCode())
                //企业
                .companyId(bbctPushSignatoryInfoVo.getCompanyId())
                .companyIDType(bbctPushSignatoryInfoVo.getCompanyIDType())
                .socialCreditCode(bbctPushSignatoryInfoVo.getSocialCreditCode())
                .companyName(bbctPushSignatoryInfoVo.getCompanyName())
                .companyCustomerNo(bbctPushSignatoryInfoVo.getCompanyCustomerNo())
                .companySupplierNo(bbctPushSignatoryInfoVo.getCompanySupplierNo())
                .companySupplierName(bbctPushSignatoryInfoVo.getCompanySupplierName())
                .authorizedAgent(bbctPushSignatoryInfoVo.getAuthorizedAgent())
                .authorizedAgentMobile(bbctPushSignatoryInfoVo.getAuthorizedAgentMobile())

                //合同
                .contractId(vo.getContractId())
                .contractClassification(vo.getContractClassification())
                .contractType(vo.getContractType())
                .contractStatus(vo.getContractStatus())
                .contractBeginDate(vo.getContractBeginDate())
                .contractEndDate(vo.getContractEndDate())
                .contractSignTime(vo.getContractSignTime())
                .contractCommencementDate(vo.getContractCommencementDate())
                .contractPriceUnit(vo.getContractPriceUnit())
                .contractPricePeriod(vo.getContractPricePeriod())
                .contractArea(vo.getContractArea())
                .approver(vo.getApprover())
                .approveTime(vo.getApproveTime())
                //项目相关
                .projectList(projectList)
                //企业银行
                .publicRecordNo(bbctPushSignatoryInfoVo.getPublicRecordNo())
                .companyBankName(bbctPushSignatoryInfoVo.getCompanyBankName())
                .companyBankCode(bbctPushSignatoryInfoVo.getCompanyBankCode())
                .companyBankBranchName(bbctPushSignatoryInfoVo.getCompanyBankBranchName())
                .companyBankBranchCode(bbctPushSignatoryInfoVo.getCompanyBankBranchCode())
                .companyBankAccountName(bbctPushSignatoryInfoVo.getCompanyBankAccountName())
                .companyBankAccountNo(bbctPushSignatoryInfoVo.getCompanyBankAccountNo())
                .companyTaxNo(bbctPushSignatoryInfoVo.getCompanyTaxNo())

                .build();


        bankRequestVo.setData(billSingleReletParamsRequest);

        ChargeRespondVo result;
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getDefault());
        if (yecaiFeign) {
            log.info("===================================================================================================================");
            try {
                log.info("合同contractId"+vo.getContractId()+",3.28趸租大合同续租生成账单接口, 请求参数(工银feign)：" + mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            result = bfipChargeFeignClient.singleRelet(bankRequestVo);
        } else {
            String url = yecaiUrl + "/charge/v1/bill/singleRelet";
            try {
                result = restTemplateUtil.postJsonStringByVo(url, mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                throw new McpException("格式转化错误");
            }
        }
        log.info("合同contractId"+vo.getContractId()+",3.28趸租大合同续租生成账单接口,工银返回:"+JSONObject.toJSONString(result));

        if (!"00000".equals(result.getCode())) {
            throw new McpException("*提示:" + result.getMessage());
        }

        return result.getCode();
    }


    /**
     * 对接工银 3.27. 趸租管理协议生成账单接口和独立管理协议接口
     */
    @Override
    public String billAgreementCreate(BbctPushInfoVo vo) {
        //请求实体
        BankRequestVo<BillAgreementCreateParamsRequest> bankRequestVo = new BankRequestVo<>();

        //合同中心传递的--产品信息列表
        List<BbctPushProductInfoVo> productInfoList = vo.getProductInfoList();
        BbctPushProductInfoVo bbctPushProductInfoVo = getBbctPushProductInfo(productInfoList, 0);
        //合同中心传递的--签约方信息
        List<BbctPushSignatoryInfoVo> signatoryInfoList = vo.getSignatoryInfoList();
        //从产品信息列表里面取--兼容--
        if(signatoryInfoList == null || signatoryInfoList.size() == 0){
            signatoryInfoList = bbctPushProductInfoVo.getSignatoryInfoList();
        }

        BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(signatoryInfoList, 0);
        BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo2 = getBbctPushSignatoryInfo(signatoryInfoList, 1);
        BbctPushSignatoryInfoVo enterpriseSignatoryInfoVo = new BbctPushSignatoryInfoVo();
        BbctPushSignatoryInfoVo personSignatoryInfoVo = new BbctPushSignatoryInfoVo();
        //找到企业、个人
        if(bbctPushSignatoryInfoVo2 == null){
            //看3.3散租接口是赋值在企业上的，不清楚之前逻辑了
            enterpriseSignatoryInfoVo = bbctPushSignatoryInfoVo;
        }else{
            if(PaymentEnums.SIGNATORYTYPE_ENTERPRISE.getCode().equals(bbctPushSignatoryInfoVo.getSignatoryType())){
                enterpriseSignatoryInfoVo = bbctPushSignatoryInfoVo;
                personSignatoryInfoVo = bbctPushSignatoryInfoVo2;
            }else{
                enterpriseSignatoryInfoVo = bbctPushSignatoryInfoVo2;
                personSignatoryInfoVo = bbctPushSignatoryInfoVo;
            }
        }

        //3	chargeSubjectList	计费科目列表  租金和押金
        ChargeSubjectParamsRequest chargeSubjecParamsRequestOne = getChargeSubject(vo,enterpriseSignatoryInfoVo,personSignatoryInfoVo,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_ONE.getCode());
        ChargeSubjectParamsRequest chargeSubjecParamsRequestTwo = getChargeSubject(vo,enterpriseSignatoryInfoVo,personSignatoryInfoVo,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode());

        List<ChargeSubjectParamsRequest> chargeSubjectParamsRequestList = new ArrayList<>();
        List<BbctPushChargeSubjectVo> furnitureSubjectVos = bbctPushProductInfoVo.getChargeSubjectList().stream()
                .filter(element ->  PaymentEnums.CHARGE_SUBJECT_NO_THREE.getCode().equals(element.getChargeSubjectNo()))
                .collect(Collectors.toList());
        ChargeSubjectParamsRequest chargeSubjecParamsRequestThree = null;
        if(furnitureSubjectVos!=null && furnitureSubjectVos.size() > 0){
            chargeSubjecParamsRequestThree = getChargeSubject(vo,enterpriseSignatoryInfoVo,personSignatoryInfoVo,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_THREE.getCode());
        }
        if(chargeSubjecParamsRequestOne != null && StringUtils.isNotBlank(chargeSubjecParamsRequestOne.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestOne);
        }
        if(chargeSubjecParamsRequestTwo != null && StringUtils.isNotBlank(chargeSubjecParamsRequestTwo.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestTwo);
        }
        if(chargeSubjecParamsRequestThree != null && StringUtils.isNotBlank(chargeSubjecParamsRequestThree.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestThree);
        }

        //最终发送对象
        BillAgreementCreateParamsRequest billAgreementCreateParamsRequest = BillAgreementCreateParamsRequest.builder()
                .historyData(vo.getHistoryData())

                .parentContractId(vo.getRelationContractCode())

                .chargeSubjectList(chargeSubjectParamsRequestList)
//                .chargeSubjecParamsRequest(chargeSubjecParamsRequestOne)
//                .chargeSubjecParamsRequest(chargeSubjecParamsRequestTwo)
                //租户
                .tenantBankName(personSignatoryInfoVo.getTenantBankName())
                .tenantBankCode(personSignatoryInfoVo.getTenantBankCode())
                .tenantBankAccountName(personSignatoryInfoVo.getTenantBankAccountName())
                .tenantBankAccountNo(personSignatoryInfoVo.getTenantBankAccountNo())
                .withholding(personSignatoryInfoVo.getWithholding())
                .withholdingSummary(PaymentEnums.WITHHOLDING_YES.getCode().equals(personSignatoryInfoVo.getWithholding()) ? "房租" : null)
                .withholdingRemark(PaymentEnums.WITHHOLDING_YES.getCode().equals(personSignatoryInfoVo.getWithholding()) ? "3" : null)
                .agreementNo(StringUtils.isNotBlank(personSignatoryInfoVo.getAgreementNo())?personSignatoryInfoVo.getAgreementNo():"0000")
                .tenantName(personSignatoryInfoVo.getTenantName())
                .tenantMobile(personSignatoryInfoVo.getTenantMobile())
                .tenantId(personSignatoryInfoVo.getTenantId())
                .tenantIDType(personSignatoryInfoVo.getTenantIDType())
                .idNumber(personSignatoryInfoVo.getIdNumber())
                .mailUrl(personSignatoryInfoVo.getMailUrl())
                .publicRecordNo(personSignatoryInfoVo.getPublicRecordNo())
                .tenantCustomerNo(personSignatoryInfoVo.getTenantCustomerNo())
                .tenantSupplierNo(personSignatoryInfoVo.getTenantSupplierNo())
                .tenantSupplierName(personSignatoryInfoVo.getTenantSupplierName())
                //企业
                .companyId(enterpriseSignatoryInfoVo.getCompanyId())
                .companyIDType(enterpriseSignatoryInfoVo.getCompanyIDType())
                .socialCreditCode(enterpriseSignatoryInfoVo.getSocialCreditCode())
                .companyName(enterpriseSignatoryInfoVo.getCompanyName())
                .companyCustomerNo(enterpriseSignatoryInfoVo.getCompanyCustomerNo())
                .companySupplierNo(enterpriseSignatoryInfoVo.getCompanySupplierNo())
                .companySupplierName(enterpriseSignatoryInfoVo.getCompanySupplierName())
                .authorizedAgent(enterpriseSignatoryInfoVo.getAuthorizedAgent())
                .authorizedAgentMobile(enterpriseSignatoryInfoVo.getAuthorizedAgentMobile())
                //项目
                .projectId(bbctPushProductInfoVo.getProjectId())
                .projectNo(StringUtils.isNotBlank(bbctPushProductInfoVo.getProjectNo()) ? bbctPushProductInfoVo.getProjectNo() : bbctPushProductInfoVo.getProjectId())
                .projectName(bbctPushProductInfoVo.getProjectName())
                .projectShortName(bbctPushProductInfoVo.getProjectShortName())
                .operateEntityType(bbctPushProductInfoVo.getOperateEntityType())
                .operateEntityName(bbctPushProductInfoVo.getOperateEntityName())
                .operateUnitBusinessNo(bbctPushProductInfoVo.getOperateUnitBusinessNo())
                .operateUnitNo(bbctPushProductInfoVo.getOperateUnitNo())
                .operateUnitName(bbctPushProductInfoVo.getOperateUnitName())
                .projectAreaBusinessNo(bbctPushProductInfoVo.getProjectAreaBusinessNo())
                .projectAreaNo(bbctPushProductInfoVo.getProjectAreaNo())
                .projectAreaName(bbctPushProductInfoVo.getProjectAreaName())
                .projectFormat(bbctPushProductInfoVo.getProjectFormat())
                .projectEstate(bbctPushProductInfoVo.getProjectEstate())
                .projectDistrict("99999")
                .projectLocation("99999")
                //房屋
                .houseId(bbctPushProductInfoVo.getHouseId())
                .houseNo(StringUtils.isNotBlank(bbctPushProductInfoVo.getHouseNo()) ? bbctPushProductInfoVo.getHouseNo() : bbctPushProductInfoVo.getHouseId())
                .houseName(bbctPushProductInfoVo.getHouseName())
                .buildingNo(bbctPushProductInfoVo.getBuildingNo())
                .unitNo(bbctPushProductInfoVo.getUnitNo())
                .floorNo(bbctPushProductInfoVo.getCurrentFloorNo() + "/" + bbctPushProductInfoVo.getTotalFloorNo())
                .roomNo(bbctPushProductInfoVo.getRoomNo())
                .houseType(bbctPushProductInfoVo.getHouseType())
                .houseOrientation(bbctPushProductInfoVo.getHouseOrientation())
                //合同
                .contractId(vo.getContractId())
                .oldContractId(vo.getOldContractId())
                .contractClassification(vo.getContractClassification())
                .contractType(vo.getContractType())
                .parkContractType(vo.getParkContractType())
                .contractStatus(vo.getContractStatus())
                .contractBeginDate(vo.getContractBeginDate())
                .contractEndDate(vo.getContractEndDate())
                .contractSignTime(vo.getContractSignTime())
                .contractCommencementDate(vo.getContractCommencementDate())
                .contractPriceUnit(vo.getContractPriceUnit())
                .contractPricePeriod(vo.getContractPricePeriod())
                .contractArea(vo.getContractArea())
                .roomType(bbctPushProductInfoVo.getRoomType())
                .approver(vo.getApprover())
                .approveTime(vo.getApproveTime())

                .companyBankName(bbctPushSignatoryInfoVo.getCompanyBankName())
                .companyBankCode(bbctPushSignatoryInfoVo.getCompanyBankCode())
                .companyBankBranchName(bbctPushSignatoryInfoVo.getCompanyBankBranchName())
                .companyBankBranchCode(bbctPushSignatoryInfoVo.getCompanyBankBranchCode())
                .companyBankAccountName(bbctPushSignatoryInfoVo.getCompanyBankAccountName())
                .companyBankAccountNo(bbctPushSignatoryInfoVo.getCompanyBankAccountNo())
                .companyTaxNo(bbctPushSignatoryInfoVo.getCompanyTaxNo())

                //reits
                .beforeContractId(vo.getBeforeContractId())
                .beforeProjectId(vo.getBeforeProjectId())
                .sceneType(vo.getSceneType())

                //区域
//                .houseArea(bbctPushProductInfoVo.getHouseArea())

                .noCompleteType(vo.getNoCompleteType())

                .build();

        //家具
        List<BbctFurnitureRentalVo> furnitureRentalList = bbctPushProductInfoVo.getFurnitureRentalList();
        List<FurnitureRentalParamsRequest> furnitureRentalParamsRequestList = new ArrayList<>();
        if (furnitureRentalList != null && furnitureRentalList.size() > 0) {
            for(BbctFurnitureRentalVo bbctFurnitureRentalVo : furnitureRentalList){
                FurnitureRentalParamsRequest furnitureRentalParamsRequest = FurnitureRentalParamsRequest.builder()
                        .furnitureId(bbctFurnitureRentalVo.getFurnitureId())
                        .furnitureName(bbctFurnitureRentalVo.getFurnitureName())
                        .furnitureRental(bbctFurnitureRentalVo.getFurnitureRental())
                        .build();
                furnitureRentalParamsRequestList.add(furnitureRentalParamsRequest);
            }
        } else {
            furnitureRentalParamsRequestList = null;
        }
        billAgreementCreateParamsRequest.setFurnitureRentalList(furnitureRentalParamsRequestList);

        bankRequestVo.setData(billAgreementCreateParamsRequest);

        ChargeRespondVo result;
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getDefault());
        if (yecaiFeign) {
            log.info("===================================================================================================================");
            try {
                log.info("合同contractId"+vo.getContractId()+",3.27趸租管理协议生成账单接口和独立管理协议接口, 请求参数(工银feign)：" + mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            result = bfipChargeFeignClient.billAgreementCreate(bankRequestVo);
        } else {
            String url = yecaiUrl + "/charge/v1/bill/relet/agreement/create";
            try {
                result = restTemplateUtil.postJsonStringByVo(url, mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                throw new McpException("格式转化错误");
            }
        }
        log.info("合同contractId"+vo.getContractId()+",3.27趸租管理协议生成账单接口和独立管理协议接口,工银返回:"+JSONObject.toJSONString(result));

        if (!"00000".equals(result.getCode())) {
            throw new McpException("*提示:" + result.getMessage());
        }

        return result.getCode();
    }


    /**
     * 对接工银 3.34. 管理协议续签生成账单
     */
    @Override
    public String agreementRelet(BbctPushInfoVo vo) {
        //请求实体
        BankRequestVo<BillAgreementReletCreateParamsRequest> bankRequestVo = new BankRequestVo<>();

        //合同中心传递的--产品信息列表
        List<BbctPushProductInfoVo> productInfoList = vo.getProductInfoList();
        BbctPushProductInfoVo bbctPushProductInfoVo = getBbctPushProductInfo(productInfoList, 0);
        //合同中心传递的--签约方信息
        List<BbctPushSignatoryInfoVo> signatoryInfoList = vo.getSignatoryInfoList();

        //从产品信息列表里面取--兼容--
        if(signatoryInfoList == null || signatoryInfoList.size() == 0){
            signatoryInfoList = bbctPushProductInfoVo.getSignatoryInfoList();
        }

        BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(signatoryInfoList, 0);
        BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo2 = getBbctPushSignatoryInfo(signatoryInfoList, 1);
        BbctPushSignatoryInfoVo enterpriseSignatoryInfoVo = new BbctPushSignatoryInfoVo();
        BbctPushSignatoryInfoVo personSignatoryInfoVo = new BbctPushSignatoryInfoVo();
        //找到企业、个人
        if(bbctPushSignatoryInfoVo2 == null){
            //看3.3散租接口是赋值在企业上的，不清楚之前逻辑了
            enterpriseSignatoryInfoVo = bbctPushSignatoryInfoVo;
        }else{
            if(PaymentEnums.SIGNATORYTYPE_ENTERPRISE.getCode().equals(bbctPushSignatoryInfoVo.getSignatoryType())){
                enterpriseSignatoryInfoVo = bbctPushSignatoryInfoVo;
                personSignatoryInfoVo = bbctPushSignatoryInfoVo2;
            }else{
                enterpriseSignatoryInfoVo = bbctPushSignatoryInfoVo2;
                personSignatoryInfoVo = bbctPushSignatoryInfoVo;
            }
        }

        //3	chargeSubjectList	计费科目列表  租金和押金
        ChargeSubjectParamsRequest chargeSubjecParamsRequestOne = getChargeSubject(vo,enterpriseSignatoryInfoVo,personSignatoryInfoVo,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_ONE.getCode());
        ChargeSubjectParamsRequest chargeSubjecParamsRequestTwo = getChargeSubject(vo,enterpriseSignatoryInfoVo,personSignatoryInfoVo,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode());
        List<ChargeSubjectParamsRequest> chargeSubjectParamsRequestList = new ArrayList<>();
        if(chargeSubjecParamsRequestOne != null && StringUtils.isNotBlank(chargeSubjecParamsRequestOne.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestOne);
        }
        if(chargeSubjecParamsRequestTwo != null && StringUtils.isNotBlank(chargeSubjecParamsRequestTwo.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestTwo);
        }

        //最终发送对象
        BillAgreementReletCreateParamsRequest billAgreementReletCreateParamsRequest = BillAgreementReletCreateParamsRequest.builder()
                .originalContractId(vo.getParentContractCode())

                .parentContractId(vo.getRelationContractCode())

                .chargeSubjectList(chargeSubjectParamsRequestList)
//                .chargeSubjecParamsRequest(chargeSubjecParamsRequestOne)
//                .chargeSubjecParamsRequest(chargeSubjecParamsRequestTwo)
                //租户
                .tenantBankName(personSignatoryInfoVo.getTenantBankName())
                .tenantBankCode(personSignatoryInfoVo.getTenantBankCode())
                .tenantBankAccountName(personSignatoryInfoVo.getTenantBankAccountName())
                .tenantBankAccountNo(personSignatoryInfoVo.getTenantBankAccountNo())
                .withholding(personSignatoryInfoVo.getWithholding())
                .withholdingSummary(PaymentEnums.WITHHOLDING_YES.getCode().equals(personSignatoryInfoVo.getWithholding()) ? "房租" : null)
                .withholdingRemark(PaymentEnums.WITHHOLDING_YES.getCode().equals(personSignatoryInfoVo.getWithholding()) ? "3" : null)
                .agreementNo(StringUtils.isNotBlank(personSignatoryInfoVo.getAgreementNo())?personSignatoryInfoVo.getAgreementNo():"0000")
                .tenantName(personSignatoryInfoVo.getTenantName())
                .tenantMobile(personSignatoryInfoVo.getTenantMobile())
                .tenantId(personSignatoryInfoVo.getTenantId())
                .tenantIDType(personSignatoryInfoVo.getTenantIDType())
                .idNumber(personSignatoryInfoVo.getIdNumber())
                .mailUrl(personSignatoryInfoVo.getMailUrl())
                .publicRecordNo(personSignatoryInfoVo.getPublicRecordNo())
                .tenantCustomerNo(personSignatoryInfoVo.getTenantCustomerNo())
                .tenantSupplierNo(personSignatoryInfoVo.getTenantSupplierNo())
                .tenantSupplierName(personSignatoryInfoVo.getTenantSupplierName())
                //企业
                .companyId(enterpriseSignatoryInfoVo.getCompanyId())
                .companyIDType(enterpriseSignatoryInfoVo.getCompanyIDType())
                .socialCreditCode(enterpriseSignatoryInfoVo.getSocialCreditCode())
                .companyName(enterpriseSignatoryInfoVo.getCompanyName())
                .companyCustomerNo(enterpriseSignatoryInfoVo.getCompanyCustomerNo())
                .companySupplierNo(enterpriseSignatoryInfoVo.getCompanySupplierNo())
                .companySupplierName(enterpriseSignatoryInfoVo.getCompanySupplierName())
                .authorizedAgent(enterpriseSignatoryInfoVo.getAuthorizedAgent())
                .authorizedAgentMobile(enterpriseSignatoryInfoVo.getAuthorizedAgentMobile())
                //项目
                .projectId(bbctPushProductInfoVo.getProjectId())
                .projectNo(StringUtils.isNotBlank(bbctPushProductInfoVo.getProjectNo()) ? bbctPushProductInfoVo.getProjectNo() : bbctPushProductInfoVo.getProjectId())
                .projectName(bbctPushProductInfoVo.getProjectName())
                .projectShortName(bbctPushProductInfoVo.getProjectShortName())
                .operateEntityType(bbctPushProductInfoVo.getOperateEntityType())
                .operateEntityName(bbctPushProductInfoVo.getOperateEntityName())
                .operateUnitBusinessNo(bbctPushProductInfoVo.getOperateUnitBusinessNo())
                .operateUnitNo(bbctPushProductInfoVo.getOperateUnitNo())
                .operateUnitName(bbctPushProductInfoVo.getOperateUnitName())
                .projectAreaBusinessNo(bbctPushProductInfoVo.getProjectAreaBusinessNo())
                .projectAreaNo(bbctPushProductInfoVo.getProjectAreaNo())
                .projectAreaName(bbctPushProductInfoVo.getProjectAreaName())
                .projectFormat(bbctPushProductInfoVo.getProjectFormat())
                .projectEstate(bbctPushProductInfoVo.getProjectEstate())
                .projectDistrict("99999")
                .projectLocation("99999")
                //房屋
                .houseId(bbctPushProductInfoVo.getHouseId())
                .houseNo(StringUtils.isNotBlank(bbctPushProductInfoVo.getHouseNo()) ? bbctPushProductInfoVo.getHouseNo() : bbctPushProductInfoVo.getHouseId())
                .houseName(bbctPushProductInfoVo.getHouseName())
                .buildingNo(bbctPushProductInfoVo.getBuildingNo())
                .unitNo(bbctPushProductInfoVo.getUnitNo())
                .floorNo(bbctPushProductInfoVo.getCurrentFloorNo() + "/" + bbctPushProductInfoVo.getTotalFloorNo())
                .roomNo(bbctPushProductInfoVo.getRoomNo())
                .houseType(bbctPushProductInfoVo.getHouseType())
                .houseOrientation(bbctPushProductInfoVo.getHouseOrientation())
                //合同
                .contractId(vo.getContractId())
                .oldContractId(vo.getOldContractId())
                .contractClassification(vo.getContractClassification())
                .contractType(vo.getContractType())
                .contractStatus(vo.getContractStatus())
                .contractBeginDate(vo.getContractBeginDate())
                .contractEndDate(vo.getContractEndDate())
                .contractSignTime(vo.getContractSignTime())
                .contractCommencementDate(vo.getContractCommencementDate())
                .contractPriceUnit(vo.getContractPriceUnit())
                .contractPricePeriod(vo.getContractPricePeriod())
                .contractArea(vo.getContractArea())
                .roomType(bbctPushProductInfoVo.getRoomType())
                .approver(vo.getApprover())
                .approveTime(vo.getApproveTime())

                .companyBankName(bbctPushSignatoryInfoVo.getCompanyBankName())
                .companyBankCode(bbctPushSignatoryInfoVo.getCompanyBankCode())
                .companyBankBranchName(bbctPushSignatoryInfoVo.getCompanyBankBranchName())
                .companyBankBranchCode(bbctPushSignatoryInfoVo.getCompanyBankBranchCode())
                .companyBankAccountName(bbctPushSignatoryInfoVo.getCompanyBankAccountName())
                .companyBankAccountNo(bbctPushSignatoryInfoVo.getCompanyBankAccountNo())
                .companyTaxNo(bbctPushSignatoryInfoVo.getCompanyTaxNo())

                //区域
//                .houseArea(bbctPushProductInfoVo.getHouseArea())

                .build();

        billAgreementReletCreateParamsRequest.setFurnitureRentalList(null);

        bankRequestVo.setData(billAgreementReletCreateParamsRequest);

        ChargeRespondVo result;
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getDefault());
        if (yecaiFeign) {
            log.info("===================================================================================================================");
            try {
                log.info("合同contractId"+vo.getContractId()+",3.34管理协议续签生成账单接口, 请求参数(工银feign)：" + mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            result = bfipChargeFeignClient.agreementRelet(bankRequestVo);
        } else {
            String url = yecaiUrl + "/charge/v1/bill/agreementRelet";
            try {
                result = restTemplateUtil.postJsonStringByVo(url, mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                throw new McpException("格式转化错误");
            }
        }
        log.info("合同contractId"+vo.getContractId()+",3.34管理协议续签生成账单接口,工银返回:"+JSONObject.toJSONString(result));

        if (!"00000".equals(result.getCode())) {
            throw new McpException("*提示:" + result.getMessage());
        }

        return result.getCode();
    }


    /**
     * 3.30 空置费账单
     * @param billVacancyFeeCreateParamsVo
     * @return
     */
    @Override
    public String billVacancyFeeCreate(BillVacancyFeeCreateParamsVo billVacancyFeeCreateParamsVo) {
        //请求实体
        BankRequestVo<BillVacancyFeeCreateParamsVo> bankRequestVo = new BankRequestVo<>();
        bankRequestVo.setData(billVacancyFeeCreateParamsVo);

        ChargeRespondVo result;
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getDefault());
        if (yecaiFeign) {
            log.info("===================================================================================================================");
            try {
                log.info("空置费账单, 请求参数(工银feign)：" + mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            result = bfipChargeFeignClient.billVacancyFeeCreate(bankRequestVo);
        } else {
            String url = yecaiUrl + "/charge/v1/bill/vacancyFee/create";
            try {
                result = restTemplateUtil.postJsonStringByVo(url, mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                throw new McpException("格式转化错误");
            }
        }
        log.info("空置费账单,工银返回:"+JSONObject.toJSONString(result));

        if (!"00000".equals(result.getCode())) {
            throw new McpException("*提示:" + result.getMessage());
        }

        return result.getCode();
    }

    /**
     * 欠费状态查询
     * @param vo
     * @return
     */
    @Override
    public Object billOverdue(ChargeArrearsVo vo) {
        //请求实体
        BankRequestVo<ChargeArrearsVo> bankRequestVo = new BankRequestVo<>();
        bankRequestVo.setData(vo);
        ChargeRespondVo result;
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getDefault());
        if (yecaiFeign) {
            log.info("===================================================================================================================");
            try {
                log.info("项目下合同欠费状态查询, 请求参数(工银feign)：" + mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            result = bfipChargeFeignClient.billOverdue(bankRequestVo);
        } else {
            String url = yecaiUrl + "/charge/v1/bill/overdue";
            try {
                result = restTemplateUtil.postJsonStringByVo(url, mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                throw new McpException("格式转化错误");
            }
        }
        if (!"00000".equals(result.getCode())) {
            throw new McpException("*提示:" + result.getMessage());
        }
        return result.getData();
    }

    /**
     * 3.37. 商业合同生成账单接口
     */
    @Override
    public String businessCreate(BbctPushInfoVo vo) {
        //请求实体
        BankRequestVo<BillBusinessCreateParamsRequest> bankRequestVo = new BankRequestVo<>();

        //合同中心传递的--产品信息列表
        List<BbctPushProductInfoVo> productInfoList = vo.getProductInfoList();
        BbctPushProductInfoVo bbctPushProductInfoVoFirst = getBbctPushProductInfo(productInfoList, 0);
        //合同中心传递的--签约方信息
        List<BbctPushSignatoryInfoVo> signatoryInfoList = vo.getSignatoryInfoList();

        if(signatoryInfoList == null || signatoryInfoList.size() == 0){
            throw new McpException("签约方信息不能为空");
        }
        BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(signatoryInfoList, 0);
        BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo2 = getBbctPushSignatoryInfo(signatoryInfoList, 1);
        BbctPushSignatoryInfoVo enterpriseSignatoryInfoVo = new BbctPushSignatoryInfoVo();
        BbctPushSignatoryInfoVo personSignatoryInfoVo = new BbctPushSignatoryInfoVo();

        //找到企业、个人
        if(bbctPushSignatoryInfoVo2 == null){
            //看3.3散租接口是赋值在企业上的，不清楚之前逻辑了
            enterpriseSignatoryInfoVo = bbctPushSignatoryInfoVo;
        }else{
            if(PaymentEnums.SIGNATORYTYPE_ENTERPRISE.getCode().equals(bbctPushSignatoryInfoVo.getSignatoryType())){
                enterpriseSignatoryInfoVo = bbctPushSignatoryInfoVo;
                personSignatoryInfoVo = bbctPushSignatoryInfoVo2;
            }else{
                enterpriseSignatoryInfoVo = bbctPushSignatoryInfoVo2;
                personSignatoryInfoVo = bbctPushSignatoryInfoVo;
            }
        }

        List<ProjectRequest> projectList = new ArrayList<>();
        //得到项目分组
        Map<String, List<BbctPushProductInfoVo>> projectsByProjectIdMap = productInfoList.stream()
                .collect(Collectors.groupingBy(BbctPushProductInfoVo::getProjectId));
        //同一个项目相关
        for (Map.Entry<String, List<BbctPushProductInfoVo>> entry : projectsByProjectIdMap.entrySet()) {
            String projectId = entry.getKey();
            List<BbctPushProductInfoVo> productInfoVos = entry.getValue();
            BbctPushProductInfoVo project = productInfoVos.get(0);
            List<RoomRequest> roomList = new ArrayList<>();
            for (BbctPushProductInfoVo bbctPushProductInfoVo : productInfoVos) {
                //3	chargeSubjectList	计费科目列表  租金和押金
                ChargeSubjectParamsRequest chargeSubjecParamsRequestOne = getChargeSubject(vo,enterpriseSignatoryInfoVo,personSignatoryInfoVo,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_ONE.getCode());
                ChargeSubjectParamsRequest chargeSubjecParamsRequestTwo = getChargeSubject(vo,enterpriseSignatoryInfoVo,personSignatoryInfoVo,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode());

                List<ChargeSubjectParamsRequest> chargeSubjectParamsRequestList = new ArrayList<>();
                List<BbctPushChargeSubjectVo> feeSubjectVos = bbctPushProductInfoVo.getChargeSubjectList().stream()
                        .filter(element ->  PaymentEnums.CHARGE_SUBJECT_NO_SEVEN.getCode().equals(element.getChargeSubjectNo()))
                        .collect(Collectors.toList());
                ChargeSubjectParamsRequest chargeSubjecParamsRequestThree = null;
                if(feeSubjectVos!=null && feeSubjectVos.size() > 0){
                    chargeSubjecParamsRequestThree = getChargeSubject(vo,enterpriseSignatoryInfoVo,personSignatoryInfoVo,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_SEVEN.getCode());
                }
                if(chargeSubjecParamsRequestOne != null && StringUtils.isNotBlank(chargeSubjecParamsRequestOne.getChargeSubjectNo())){
                    chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestOne);
                }
                if(chargeSubjecParamsRequestTwo != null && StringUtils.isNotBlank(chargeSubjecParamsRequestTwo.getChargeSubjectNo())){
                    chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestTwo);
                }
                if(chargeSubjecParamsRequestThree != null && StringUtils.isNotBlank(chargeSubjecParamsRequestThree.getChargeSubjectNo())){
                    chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestThree);
                }

                RoomRequest roomRequest = RoomRequest.builder()
                        //计费科目
                        .chargeSubjectList(chargeSubjectParamsRequestList)
//                        .chargeSubjecParamsRequest(chargeSubjecParamsRequestOne)
//                        .chargeSubjecParamsRequest(chargeSubjecParamsRequestTwo)
                        //房屋
                        .houseId(bbctPushProductInfoVo.getHouseId())
                        .houseNo(StringUtils.isNotBlank(bbctPushProductInfoVo.getHouseNo()) ? bbctPushProductInfoVo.getHouseNo() : bbctPushProductInfoVo.getHouseId())
                        .houseName(bbctPushProductInfoVo.getHouseName())
                        .buildingNo(bbctPushProductInfoVo.getBuildingNo())
                        .unitNo(bbctPushProductInfoVo.getUnitNo())
                        .floorNo(bbctPushProductInfoVo.getCurrentFloorNo() + "/" + bbctPushProductInfoVo.getTotalFloorNo())
                        .roomNo(bbctPushProductInfoVo.getRoomNo())
                        .houseType(bbctPushProductInfoVo.getHouseType())
                        .houseOrientation(bbctPushProductInfoVo.getHouseOrientation())
                        .roomType(bbctPushProductInfoVo.getRoomType())
//                        .roomArea()
                        .build();
                roomRequest.setFurnitureRentalList(null);
                roomList.add(roomRequest);
            }
            ProjectRequest projectRequest = ProjectRequest.builder()
                    //项目
                    .projectId(projectId)
                    .projectNo(StringUtils.isNotBlank(project.getProjectNo()) ? project.getProjectNo() : project.getProjectId())
                    .projectName(project.getProjectName())
                    .projectShortName(project.getProjectShortName())
                    .operateEntityType(project.getOperateEntityType())
                    .operateEntityName(project.getOperateEntityName())
                    .operateUnitBusinessNo(project.getOperateUnitBusinessNo())
                    .operateUnitNo(project.getOperateUnitNo())
                    .operateUnitName(project.getOperateUnitName())
                    .projectAreaBusinessNo(project.getProjectAreaBusinessNo())
                    .projectAreaNo(project.getProjectAreaNo())
                    .projectAreaName(project.getProjectAreaName())
                    .projectFormat(project.getProjectFormat())
                    .projectEstate(project.getProjectEstate())
                    .projectDistrict("99999")
                    .projectLocation("99999")
                    .secBusinessType(project.getSecBusinessType())
                    //房屋
                    .roomList(roomList)
                    .build();
            projectList.add(projectRequest);
        }

        BillBusinessCreateParamsRequest billBusinessCreateParamsRequest = BillBusinessCreateParamsRequest.builder()
                 //合同
                .contractId(vo.getContractId())
                .contractClassification(vo.getContractClassification())
                .contractType(vo.getContractType())
                .contractStatus(vo.getContractStatus())
                .contractBeginDate(vo.getContractBeginDate())
                .contractEndDate(vo.getContractEndDate())
                .contractSignTime(vo.getContractSignTime())
                .contractCommencementDate(vo.getContractCommencementDate())
                .contractPriceUnit(vo.getContractPriceUnit())
                .contractPricePeriod(vo.getContractPricePeriod())
                .contractArea(vo.getContractArea())
                .roomType(bbctPushProductInfoVoFirst.getRoomType())
                .approver(vo.getApprover())
                .approveTime(vo.getApproveTime())
                //租户
                .tenantId(bbctPushSignatoryInfoVo.getTenantId())
                .tenantBankName(bbctPushSignatoryInfoVo.getTenantBankName())
                .tenantBankCode(bbctPushSignatoryInfoVo.getTenantBankCode())
//                .tenantBankBranchName(null)
//                .tenantBankBrachCode(null)
                .tenantBankAccountName(bbctPushSignatoryInfoVo.getTenantBankAccountName())
                .tenantBankAccountNo(bbctPushSignatoryInfoVo.getTenantBankAccountNo())
                .withholding(bbctPushSignatoryInfoVo.getWithholding())
                .withholdingSummary(PaymentEnums.WITHHOLDING_YES.getCode().equals(bbctPushSignatoryInfoVo.getWithholding()) ? "房租" : null)
                .withholdingRemark(PaymentEnums.WITHHOLDING_YES.getCode().equals(bbctPushSignatoryInfoVo.getWithholding()) ? "3" : null)
                .agreementNo(bbctPushSignatoryInfoVo.getAgreementNo())
                .tenantName(bbctPushSignatoryInfoVo.getTenantName())
                .tenantMobile(bbctPushSignatoryInfoVo.getTenantMobile())
                .tenantIDType(bbctPushSignatoryInfoVo.getTenantIDType())
                .idNumber(bbctPushSignatoryInfoVo.getIdNumber())
                .mailUrl(bbctPushSignatoryInfoVo.getMailUrl())
                .publicRecordNo(bbctPushSignatoryInfoVo.getPublicRecordNo())
                .tenantCustomerNo(bbctPushSignatoryInfoVo.getTenantCustomerNo())
                .tenantSupplierNo(bbctPushSignatoryInfoVo.getTenantSupplierNo())
                .tenantSupplierName(bbctPushSignatoryInfoVo.getTenantSupplierName())
                //企业
                .companyId(enterpriseSignatoryInfoVo.getCompanyId())
                .companyIDType(enterpriseSignatoryInfoVo.getCompanyIDType())
                .socialCreditCode(enterpriseSignatoryInfoVo.getSocialCreditCode())
                .companyName(enterpriseSignatoryInfoVo.getCompanyName())
                .companyCustomerNo(enterpriseSignatoryInfoVo.getCompanyCustomerNo())
                .companySupplierNo(enterpriseSignatoryInfoVo.getCompanySupplierNo())
                .companySupplierName(enterpriseSignatoryInfoVo.getCompanySupplierName())
                .authorizedAgent(enterpriseSignatoryInfoVo.getAuthorizedAgent())
                .authorizedAgentMobile(enterpriseSignatoryInfoVo.getAuthorizedAgentMobile())
                .companyBankName(bbctPushSignatoryInfoVo.getCompanyBankName())
                .companyBankCode(bbctPushSignatoryInfoVo.getCompanyBankCode())
                .companyBankBranchName(bbctPushSignatoryInfoVo.getCompanyBankBranchName())
                .companyBankBranchCode(bbctPushSignatoryInfoVo.getCompanyBankBranchCode())
                .companyBankAccountName(bbctPushSignatoryInfoVo.getCompanyBankAccountName())
                .companyBankAccountNo(bbctPushSignatoryInfoVo.getCompanyBankAccountNo())
                .companyTaxNo(bbctPushSignatoryInfoVo.getCompanyTaxNo())
                //项目相关
                .projectList(projectList)
                .build();

        bankRequestVo.setData(billBusinessCreateParamsRequest);

        ChargeRespondVo result;
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getDefault());
        if (yecaiFeign) {
            log.info("===================================================================================================================");
            try {
                log.info("合同contractId"+vo.getContractId()+",3.37商业合同生成账单接口, 请求参数(工银feign)：" + mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            result = bfipChargeFeignClient.billBusinessCreate(bankRequestVo);
        } else {
            String url = yecaiUrl + "/charge/v1/bill/business/create";
            try {
                result = restTemplateUtil.postJsonStringByVo(url, mapper.writeValueAsString(bankRequestVo));

            } catch (JsonProcessingException e) {
                throw new McpException("格式转化错误");
            }
        }
        log.info("合同contractId"+vo.getContractId()+",3.37商业合同生成账单接口,工银返回:"+JSONObject.toJSONString(result));

        if (!"00000".equals(result.getCode())) {
            throw new McpException("*提示:" + result.getMessage());
        }

        return result.getCode();
    }

    /**
     * 3.48 商业合同续签生成账单
     */
    @Override
    public String businessRelet(BbctPushInfoVo vo) {
        //请求实体
        BankRequestVo<BillBusinessCreateParamsRequest> bankRequestVo = new BankRequestVo<>();

        //合同中心传递的--产品信息列表
        List<BbctPushProductInfoVo> productInfoList = vo.getProductInfoList();
        BbctPushProductInfoVo bbctPushProductInfoVoFirst = getBbctPushProductInfo(productInfoList, 0);
        //合同中心传递的--签约方信息
        List<BbctPushSignatoryInfoVo> signatoryInfoList = vo.getSignatoryInfoList();

        if(signatoryInfoList == null || signatoryInfoList.size() == 0){
            throw new McpException("签约方信息不能为空");
        }
        BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(signatoryInfoList, 0);
        BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo2 = getBbctPushSignatoryInfo(signatoryInfoList, 1);
        BbctPushSignatoryInfoVo enterpriseSignatoryInfoVo = new BbctPushSignatoryInfoVo();
        BbctPushSignatoryInfoVo personSignatoryInfoVo = new BbctPushSignatoryInfoVo();

        //找到企业、个人
        if(bbctPushSignatoryInfoVo2 == null){
            //看3.3散租接口是赋值在企业上的，不清楚之前逻辑了
            enterpriseSignatoryInfoVo = bbctPushSignatoryInfoVo;
        }else{
            if(PaymentEnums.SIGNATORYTYPE_ENTERPRISE.getCode().equals(bbctPushSignatoryInfoVo.getSignatoryType())){
                enterpriseSignatoryInfoVo = bbctPushSignatoryInfoVo;
                personSignatoryInfoVo = bbctPushSignatoryInfoVo2;
            }else{
                enterpriseSignatoryInfoVo = bbctPushSignatoryInfoVo2;
                personSignatoryInfoVo = bbctPushSignatoryInfoVo;
            }
        }

        List<ProjectRequest> projectList = new ArrayList<>();
        //得到项目分组
        Map<String, List<BbctPushProductInfoVo>> projectsByProjectIdMap = productInfoList.stream()
                .collect(Collectors.groupingBy(BbctPushProductInfoVo::getProjectId));
        //同一个项目相关
        for (Map.Entry<String, List<BbctPushProductInfoVo>> entry : projectsByProjectIdMap.entrySet()) {
            String projectId = entry.getKey();
            List<BbctPushProductInfoVo> productInfoVos = entry.getValue();
            BbctPushProductInfoVo project = productInfoVos.get(0);
            List<RoomRequest> roomList = new ArrayList<>();
            for (BbctPushProductInfoVo bbctPushProductInfoVo : productInfoVos) {
                //3	chargeSubjectList	计费科目列表  租金和押金
                ChargeSubjectParamsRequest chargeSubjecParamsRequestOne = getChargeSubject(vo,enterpriseSignatoryInfoVo,personSignatoryInfoVo,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_ONE.getCode());
                ChargeSubjectParamsRequest chargeSubjecParamsRequestTwo = getChargeSubject(vo,enterpriseSignatoryInfoVo,personSignatoryInfoVo,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode());

                List<ChargeSubjectParamsRequest> chargeSubjectParamsRequestList = new ArrayList<>();
                List<BbctPushChargeSubjectVo> feeSubjectVos = bbctPushProductInfoVo.getChargeSubjectList().stream()
                        .filter(element ->  PaymentEnums.CHARGE_SUBJECT_NO_SEVEN.getCode().equals(element.getChargeSubjectNo()))
                        .collect(Collectors.toList());
                ChargeSubjectParamsRequest chargeSubjecParamsRequestThree = null;
                if(feeSubjectVos!=null && feeSubjectVos.size() > 0){
                    chargeSubjecParamsRequestThree = getChargeSubject(vo,enterpriseSignatoryInfoVo,personSignatoryInfoVo,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_SEVEN.getCode());
                }
                if(chargeSubjecParamsRequestOne != null && StringUtils.isNotBlank(chargeSubjecParamsRequestOne.getChargeSubjectNo())){
                    chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestOne);
                }
                if(chargeSubjecParamsRequestTwo != null && StringUtils.isNotBlank(chargeSubjecParamsRequestTwo.getChargeSubjectNo())){
                    chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestTwo);
                }
                if(chargeSubjecParamsRequestThree != null && StringUtils.isNotBlank(chargeSubjecParamsRequestThree.getChargeSubjectNo())){
                    chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestThree);
                }

                RoomRequest roomRequest = RoomRequest.builder()
                        //计费科目
                        .chargeSubjectList(chargeSubjectParamsRequestList)
//                        .chargeSubjecParamsRequest(chargeSubjecParamsRequestOne)
//                        .chargeSubjecParamsRequest(chargeSubjecParamsRequestTwo)
                        //房屋
                        .houseId(bbctPushProductInfoVo.getHouseId())
                        .houseNo(StringUtils.isNotBlank(bbctPushProductInfoVo.getHouseNo()) ? bbctPushProductInfoVo.getHouseNo() : bbctPushProductInfoVo.getHouseId())
                        .houseName(bbctPushProductInfoVo.getHouseName())
                        .buildingNo(bbctPushProductInfoVo.getBuildingNo())
                        .unitNo(bbctPushProductInfoVo.getUnitNo())
                        .floorNo(bbctPushProductInfoVo.getCurrentFloorNo() + "/" + bbctPushProductInfoVo.getTotalFloorNo())
                        .roomNo(bbctPushProductInfoVo.getRoomNo())
                        .houseType(bbctPushProductInfoVo.getHouseType())
                        .houseOrientation(bbctPushProductInfoVo.getHouseOrientation())
                        .roomType(bbctPushProductInfoVo.getRoomType())
//                        .roomArea()
                        .build();
                roomRequest.setFurnitureRentalList(null);
                roomList.add(roomRequest);
            }
            ProjectRequest projectRequest = ProjectRequest.builder()
                    //项目
                    .projectId(projectId)
                    .projectNo(StringUtils.isNotBlank(project.getProjectNo()) ? project.getProjectNo() : project.getProjectId())
                    .projectName(project.getProjectName())
                    .projectShortName(project.getProjectShortName())
                    .operateEntityType(project.getOperateEntityType())
                    .operateEntityName(project.getOperateEntityName())
                    .operateUnitBusinessNo(project.getOperateUnitBusinessNo())
                    .operateUnitNo(project.getOperateUnitNo())
                    .operateUnitName(project.getOperateUnitName())
                    .projectAreaBusinessNo(project.getProjectAreaBusinessNo())
                    .projectAreaNo(project.getProjectAreaNo())
                    .projectAreaName(project.getProjectAreaName())
                    .projectFormat(project.getProjectFormat())
                    .projectEstate(project.getProjectEstate())
                    .projectDistrict("99999")
                    .projectLocation("99999")
                    .secBusinessType(project.getSecBusinessType())
                    //房屋
                    .roomList(roomList)
                    .build();
            projectList.add(projectRequest);
        }

        BillBusinessCreateParamsRequest billBusinessCreateParamsRequest = BillBusinessCreateParamsRequest.builder()
                //合同
                .contractId(vo.getContractId())
                .contractClassification(vo.getContractClassification())
                .contractType(vo.getContractType())
                .contractStatus(vo.getContractStatus())
                .contractBeginDate(vo.getContractBeginDate())
                .contractEndDate(vo.getContractEndDate())
                .contractSignTime(vo.getContractSignTime())
                .contractCommencementDate(vo.getContractCommencementDate())
                .contractPriceUnit(vo.getContractPriceUnit())
                .contractPricePeriod(vo.getContractPricePeriod())
                .contractArea(vo.getContractArea())
                .roomType(bbctPushProductInfoVoFirst.getRoomType())
                .approver(vo.getApprover())
                .approveTime(vo.getApproveTime())
                //租户
                .tenantId(bbctPushSignatoryInfoVo.getTenantId())
                .tenantBankName(bbctPushSignatoryInfoVo.getTenantBankName())
                .tenantBankCode(bbctPushSignatoryInfoVo.getTenantBankCode())
//                .tenantBankBranchName(null)
//                .tenantBankBrachCode(null)
                .tenantBankAccountName(bbctPushSignatoryInfoVo.getTenantBankAccountName())
                .tenantBankAccountNo(bbctPushSignatoryInfoVo.getTenantBankAccountNo())
                .withholding(bbctPushSignatoryInfoVo.getWithholding())
                .withholdingSummary(PaymentEnums.WITHHOLDING_YES.getCode().equals(bbctPushSignatoryInfoVo.getWithholding()) ? "房租" : null)
                .withholdingRemark(PaymentEnums.WITHHOLDING_YES.getCode().equals(bbctPushSignatoryInfoVo.getWithholding()) ? "3" : null)
                .agreementNo(bbctPushSignatoryInfoVo.getAgreementNo())
                .tenantName(bbctPushSignatoryInfoVo.getTenantName())
                .tenantMobile(bbctPushSignatoryInfoVo.getTenantMobile())
                .tenantIDType(bbctPushSignatoryInfoVo.getTenantIDType())
                .idNumber(bbctPushSignatoryInfoVo.getIdNumber())
                .mailUrl(bbctPushSignatoryInfoVo.getMailUrl())
                .publicRecordNo(bbctPushSignatoryInfoVo.getPublicRecordNo())
                .tenantCustomerNo(bbctPushSignatoryInfoVo.getTenantCustomerNo())
                .tenantSupplierNo(bbctPushSignatoryInfoVo.getTenantSupplierNo())
                .tenantSupplierName(bbctPushSignatoryInfoVo.getTenantSupplierName())
                //企业
                .companyId(enterpriseSignatoryInfoVo.getCompanyId())
                .companyIDType(enterpriseSignatoryInfoVo.getCompanyIDType())
                .socialCreditCode(enterpriseSignatoryInfoVo.getSocialCreditCode())
                .companyName(enterpriseSignatoryInfoVo.getCompanyName())
                .companyCustomerNo(enterpriseSignatoryInfoVo.getCompanyCustomerNo())
                .companySupplierNo(enterpriseSignatoryInfoVo.getCompanySupplierNo())
                .companySupplierName(enterpriseSignatoryInfoVo.getCompanySupplierName())
                .authorizedAgent(enterpriseSignatoryInfoVo.getAuthorizedAgent())
                .authorizedAgentMobile(enterpriseSignatoryInfoVo.getAuthorizedAgentMobile())
                .companyBankName(bbctPushSignatoryInfoVo.getCompanyBankName())
                .companyBankCode(bbctPushSignatoryInfoVo.getCompanyBankCode())
                .companyBankBranchName(bbctPushSignatoryInfoVo.getCompanyBankBranchName())
                .companyBankBranchCode(bbctPushSignatoryInfoVo.getCompanyBankBranchCode())
                .companyBankAccountName(bbctPushSignatoryInfoVo.getCompanyBankAccountName())
                .companyBankAccountNo(bbctPushSignatoryInfoVo.getCompanyBankAccountNo())
                .companyTaxNo(bbctPushSignatoryInfoVo.getCompanyTaxNo())
                //项目相关
                .projectList(projectList)

                //商业续租新增字段
                .originalContractId(vo.getParentContractCode())
                .depositReturn(vo.getDepositReturn())

                .build();

        bankRequestVo.setData(billBusinessCreateParamsRequest);

        ChargeRespondVo result;
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getDefault());
        if (yecaiFeign) {
            log.info("===================================================================================================================");
            try {
                log.info("合同contractId"+vo.getContractId()+",3.48商业合同续签生成账单接口, 请求参数(工银feign)：" + mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            result = bfipChargeFeignClient.billBusinessRelet(bankRequestVo);
        } else {
            String url = yecaiUrl + "/charge/v1/bill/business/relet";
            try {
                result = restTemplateUtil.postJsonStringByVo(url, mapper.writeValueAsString(bankRequestVo));

            } catch (JsonProcessingException e) {
                throw new McpException("格式转化错误");
            }
        }
        log.info("合同contractId"+vo.getContractId()+",3.48商业合同续签生成账单接口,工银返回:"+JSONObject.toJSONString(result));

        if (!"00000".equals(result.getCode())) {
            throw new McpException("*提示:" + result.getMessage());
        }

        return result.getCode();
    }




    /**
     * 合同变更
     * @param vo
     * @return
     */
    @Override
    public String updateByContractAll(BbctPushInfoVo vo) {
        BbpmMainLesseeExcelVo bbpmMainLesseeExcelVo = new BbpmMainLesseeExcelVo();
        try {

            String traceId = (String) SystemContextHolder.getCurrentContext().get("traceId");
            bbpmMainLesseeExcelVo.setTraceId(traceId);
            ObjectMapper objm = new ObjectMapper();
            objm.setTimeZone(TimeZone.getDefault());
            bbpmMainLesseeExcelVo.setReqBody(objm.writeValueAsString(vo));

            //启动备用字段fields1  设置以前合同编号
            bbpmMainLesseeExcelVo.setFields1(vo.getParentContractCode());

            //请求实体
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            ParentRequest<ChangeContractRequest> parentRequest = new ParentRequest<>();
            parentRequest.setTime(sdf.format(new Date()));
            parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));

            //设置变更类型
            String chageType = "";
            //主承租人变更
            if (PaymentEnums.CONTRACTSOURCETYPE_MAIN_LESSEE.getCode().equals(vo.getContractSourceType())) {
                chageType = PaymentEnums.GY_CHANGETYPE_MAIN_LESSEE.getCode();
                //散租--
                if (PaymentEnums.TYPE_OF_CONTRACT_LOOSE.getCode().equals(vo.getContractType())) {
                    //合同中心传递的--产品信息列表
                    List<BbctPushProductInfoVo> productInfoList = vo.getProductInfoList();
                    BbctPushProductInfoVo bbctPushProductInfoVo = getBbctPushProductInfo(productInfoList, 0);
                    //合同中心传递的--签约方信息
                    List<BbctPushSignatoryInfoVo> signatoryInfoList = vo.getSignatoryInfoList();
                    //从产品信息列表里面取--兼容--
                    if(signatoryInfoList == null || signatoryInfoList.size() == 0){
                        signatoryInfoList = bbctPushProductInfoVo.getSignatoryInfoList();
                    }
                    BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(signatoryInfoList, 0);

                    bbpmMainLesseeExcelVo.setContractNo(vo.getContractId());
                    //请求实体
//            BankRequestVo<ChangeContractRequest> bankRequestVo = new BankRequestVo<>();
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

                    TenantChangeDto tenantChangeDto = TenantChangeDto.builder()
                            .beforeContractCode(vo.getParentContractCode())
                            .contractCode(vo.getContractId())
                            .contractBeginDate(simpleDateFormat.parse(simpleDateFormat.format(vo.getContractBeginDate())))
                            .contractEndDate(simpleDateFormat.parse(simpleDateFormat.format(vo.getContractEndDate())))
                            .contractSignTime(simpleDateFormat.parse(simpleDateFormat.format(vo.getContractSignTime())))
                            .contractCommencementDate(simpleDateFormat.parse(simpleDateFormat.format(vo.getContractCommencementDate())))
                            .tenantBankName(bbctPushSignatoryInfoVo.getTenantBankName())
                            .tenantBankCode(bbctPushSignatoryInfoVo.getTenantBankCode())
                            .tenantBankBranchName(null)
                            .tenantBankBrachCode(null)
                            .tenantBankAccountName(bbctPushSignatoryInfoVo.getTenantBankAccountName())
                            .tenantBankAccountNo(bbctPushSignatoryInfoVo.getTenantBankAccountNo())
                            .withholding(bbctPushSignatoryInfoVo.getWithholding())
                            .withholdingSummary(PaymentEnums.WITHHOLDING_YES.getCode().equals(bbctPushSignatoryInfoVo.getWithholding()) ? "房租" : null)
                            .withholdingRemark(PaymentEnums.WITHHOLDING_YES.getCode().equals(bbctPushSignatoryInfoVo.getWithholding()) ? "3" : null)
                            .agreementNo(bbctPushSignatoryInfoVo.getAgreementNo())
                            .tenantName(bbctPushSignatoryInfoVo.getTenantName())
                            .tenantMobile(bbctPushSignatoryInfoVo.getTenantMobile())
                            .tenantId(bbctPushSignatoryInfoVo.getTenantId())
                            .tenantIDType(bbctPushSignatoryInfoVo.getTenantIDType())
                            .idNumber(bbctPushSignatoryInfoVo.getIdNumber())
                            .mailUrl(bbctPushSignatoryInfoVo.getMailUrl())
                            .publicRecordNo(bbctPushSignatoryInfoVo.getPublicRecordNo())
                            .tenantCustomerNo(bbctPushSignatoryInfoVo.getTenantCustomerNo())
                            .tenantSupplierNo(bbctPushSignatoryInfoVo.getTenantSupplierNo())
                            .tenantSupplierName(bbctPushSignatoryInfoVo.getTenantSupplierName())
                            .build();

                    ChangeContractRequest changeContractRequest = ChangeContractRequest.builder()
                            .changeType(chageType)
                            .projectId(bbctPushProductInfoVo.getProjectId())
                            .tenantChangeDto(tenantChangeDto)
                            .build();

                    parentRequest.setData(changeContractRequest);

                }else if(PaymentEnums.TYPE_OF_CONTRACT_MANAGER.getCode().equals(vo.getContractType())){
                    //管理协议 --
                    //合同中心传递的--产品信息列表
                    List<BbctPushProductInfoVo> productInfoList = vo.getProductInfoList();
                    BbctPushProductInfoVo bbctPushProductInfoVo = getBbctPushProductInfo(productInfoList, 0);
                    //合同中心传递的--签约方信息
                    List<BbctPushSignatoryInfoVo> signatoryInfoList = vo.getSignatoryInfoList();
                    //从产品信息列表里面取--兼容--
                    if(signatoryInfoList == null || signatoryInfoList.size() == 0){
                        signatoryInfoList = bbctPushProductInfoVo.getSignatoryInfoList();
                    }

                    BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(signatoryInfoList, 0);
                    BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo2 = getBbctPushSignatoryInfo(signatoryInfoList, 1);
                    //找到个人
                    if(PaymentEnums.SIGNATORYTYPE_ENTERPRISE.getCode().equals(bbctPushSignatoryInfoVo.getSignatoryType())){
                        bbctPushSignatoryInfoVo = bbctPushSignatoryInfoVo2;
                    }

                    bbpmMainLesseeExcelVo.setContractNo(vo.getContractId());

                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

                    TenantChangeDto tenantChangeDto = TenantChangeDto.builder()
                            .beforeContractCode(vo.getParentContractCode())
                            .contractCode(vo.getContractId())
                            .contractBeginDate(simpleDateFormat.parse(simpleDateFormat.format(vo.getContractBeginDate())))
                            .contractEndDate(simpleDateFormat.parse(simpleDateFormat.format(vo.getContractEndDate())))
                            .contractSignTime(simpleDateFormat.parse(simpleDateFormat.format(vo.getContractSignTime())))
                            .contractCommencementDate(simpleDateFormat.parse(simpleDateFormat.format(vo.getContractCommencementDate())))
                            .tenantBankName(bbctPushSignatoryInfoVo.getTenantBankName())
                            .tenantBankCode(bbctPushSignatoryInfoVo.getTenantBankCode())
                            .tenantBankBranchName(null)
                            .tenantBankBrachCode(null)
                            .tenantBankAccountName(bbctPushSignatoryInfoVo.getTenantBankAccountName())
                            .tenantBankAccountNo(bbctPushSignatoryInfoVo.getTenantBankAccountNo())
                            .withholding(bbctPushSignatoryInfoVo.getWithholding())
                            .withholdingSummary(PaymentEnums.WITHHOLDING_YES.getCode().equals(bbctPushSignatoryInfoVo.getWithholding()) ? "房租" : null)
                            .withholdingRemark(PaymentEnums.WITHHOLDING_YES.getCode().equals(bbctPushSignatoryInfoVo.getWithholding()) ? "3" : null)
                            .agreementNo(bbctPushSignatoryInfoVo.getAgreementNo())
                            .tenantName(bbctPushSignatoryInfoVo.getTenantName())
                            .tenantMobile(bbctPushSignatoryInfoVo.getTenantMobile())
                            .tenantId(bbctPushSignatoryInfoVo.getTenantId())
                            .tenantIDType(bbctPushSignatoryInfoVo.getTenantIDType())
                            .idNumber(bbctPushSignatoryInfoVo.getIdNumber())
                            .mailUrl(bbctPushSignatoryInfoVo.getMailUrl())
                            .publicRecordNo(bbctPushSignatoryInfoVo.getPublicRecordNo())
                            .tenantCustomerNo(bbctPushSignatoryInfoVo.getTenantCustomerNo())
                            .tenantSupplierNo(bbctPushSignatoryInfoVo.getTenantSupplierNo())
                            .tenantSupplierName(bbctPushSignatoryInfoVo.getTenantSupplierName())
                            .build();

                    ChangeContractRequest changeContractRequest = ChangeContractRequest.builder()
                            .changeType(chageType)
                            .projectId(bbctPushProductInfoVo.getProjectId())
                            .tenantChangeDto(tenantChangeDto)
                            .build();

                    parentRequest.setData(changeContractRequest);

                } else if (PaymentEnums.TYPE_OF_CONTRACT_SINGLERENT.getCode().equals(vo.getContractType())) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    boolean isNew = true;
                    //合同中心传递的--签约方信息
                    List<BbctPushSignatoryInfoVo> signatoryInfoList = vo.getSignatoryInfoList();
                    BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo = new BbctPushSignatoryInfoVo();
                    //--兼容--
                    if(signatoryInfoList != null && signatoryInfoList.size() > 0){
                        bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(signatoryInfoList, 0);
                        isNew = false;
                    }
                    List<ProjectRequest> projectList = new ArrayList<>();
                    //合同中心传递的--产品信息列表
                    List<BbctPushProductInfoVo> productInfoList = vo.getProductInfoList();
                    //得到项目分组
                    Map<String, List<BbctPushProductInfoVo>> projectsByProjectIdMap = productInfoList.stream()
                            .collect(Collectors.groupingBy(BbctPushProductInfoVo::getProjectId));
                    String projectId = "";
                    //同一个项目相关
                    for (Map.Entry<String, List<BbctPushProductInfoVo>> entry : projectsByProjectIdMap.entrySet()) {
                        projectId = entry.getKey();
                        List<BbctPushProductInfoVo> productInfoVos = entry.getValue();
                        BbctPushProductInfoVo project = productInfoVos.get(0);
                        List<RoomRequest> roomList = new ArrayList<>();
                        for (int m = 0; m < productInfoVos.size(); m++) {
                            BbctPushProductInfoVo bbctPushProductInfoVo = productInfoVos.get(m);
                            //从产品信息列表里面取--兼容--
                            if (isNew) {
                                signatoryInfoList = bbctPushProductInfoVo.getSignatoryInfoList().stream()
                                        .filter(element -> PaymentEnums.SIGNATORYTYPE_ENTERPRISE.getCode().equals(element.getSignatoryType()))
                                        .collect(Collectors.toList());

                                bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(signatoryInfoList, 0);
                            }
                        }
                    }

                    bbpmMainLesseeExcelVo.setContractNo(vo.getContractId());

                    ReletTenantChangeDto reletTenantChangeDto = ReletTenantChangeDto.builder()
                            .companyId(bbctPushSignatoryInfoVo.getCompanyId())
                            .companyIDType(bbctPushSignatoryInfoVo.getCompanyIDType())
                            .socialCreditCode(bbctPushSignatoryInfoVo.getSocialCreditCode())
                            .companyName(bbctPushSignatoryInfoVo.getCompanyName())
                            .companySupplierNo(bbctPushSignatoryInfoVo.getCompanySupplierNo())
                            .companySupplierName(bbctPushSignatoryInfoVo.getCompanySupplierName())
                            .companyCustomerNo(bbctPushSignatoryInfoVo.getCompanyCustomerNo())
                            .authorizedAgentMobile(bbctPushSignatoryInfoVo.getAuthorizedAgentMobile())
                            .authorizedAgent(bbctPushSignatoryInfoVo.getAuthorizedAgent())
                            .companyTaxNo(bbctPushSignatoryInfoVo.getCompanyTaxNo())
                            .companyBankName(bbctPushSignatoryInfoVo.getCompanyBankName())
                            .companyBankCode(bbctPushSignatoryInfoVo.getCompanyBankCode())
                            .companyBankBranchName(bbctPushSignatoryInfoVo.getCompanyBankBranchName())
                            .companyBankBranchCode(bbctPushSignatoryInfoVo.getCompanyBankBranchCode())
                            .companyBankAccountName(bbctPushSignatoryInfoVo.getCompanyBankAccountName())
                            .companyBankAccountNo(bbctPushSignatoryInfoVo.getCompanyBankAccountNo())
                            .beforeContractCode(vo.getParentContractCode())
                            .contractCode(vo.getContractId())
                            .contractStatus(vo.getContractStatus())
                            .contractBeginDate(simpleDateFormat.parse(simpleDateFormat.format(vo.getContractBeginDate())))
                            .contractEndDate(simpleDateFormat.parse(simpleDateFormat.format(vo.getContractEndDate())))
                            .contractSignTime(simpleDateFormat.parse(simpleDateFormat.format(vo.getContractSignTime())))
                            .contractCommencementDate(simpleDateFormat.parse(simpleDateFormat.format(vo.getContractCommencementDate())))
                            .build();

                    ChangeContractRequest changeContractRequest = ChangeContractRequest.builder()
                            .changeType(chageType)
                            .projectId(projectId)
                            .reletTenantChangeDto(reletTenantChangeDto)
                            .build();

                    parentRequest.setData(changeContractRequest);

                } else {
                    throw new McpException("合同类型:contractType不匹配");
                }

            } else if (PaymentEnums.CONTRACTSOURCETYPE_PURCHASE_AGREEMENT.getCode().equals(vo.getContractSourceType())) {
                //购房协议
                chageType = PaymentEnums.GY_CHANGETYPE_PURCHASE_AGREEMENT.getCode();
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                List<BbctPushProductInfoVo> productInfoList = vo.getProductInfoList();
                BbctPushProductInfoVo bbctPushProductInfoVo = getBbctPushProductInfo(productInfoList, 0);
                //计费科目列表
                List<BbctPushChargeSubjectVo> chargeSubjectList = bbctPushProductInfoVo.getChargeSubjectList();
                //筛选对应科目
                List<BbctPushChargeSubjectVo> bbctPushChargeSubjectVoList = chargeSubjectList.stream()
                        .filter(element -> PaymentEnums.CHARGE_SUBJECT_NO_ONE.getCode().equals(element.getChargeSubjectNo()))
                        .collect(Collectors.toList());
                BbctPushChargeSubjectVo bbctPushChargeSubjectVo = bbctPushChargeSubjectVoList.get(0);
                BigDecimal newRentStandard = getProductRent(bbctPushChargeSubjectVo);

                bbpmMainLesseeExcelVo.setContractNo(vo.getContractId());

                SignSupplementAgreementDto signSupplementAgreementDto = SignSupplementAgreementDto.builder()
                        .beforeContractCode(vo.getParentContractCode())
                        .agreementCode(vo.getContractId())
                        .newRentStandard(newRentStandard)
                        .agreementStartTime(simpleDateFormat.format(vo.getContractBeginDate()))
                        .agreementEndTime(simpleDateFormat.format(vo.getContractEndDate()))
                        .build();

                ChangeContractRequest changeContractRequest = ChangeContractRequest.builder()
                        .changeType(chageType)
                        .projectId(bbctPushProductInfoVo.getProjectId())
                        .signSupplementAgreementDTO(signSupplementAgreementDto)
                        .build();

                parentRequest.setData(changeContractRequest);
            } else if (PaymentEnums.CONTRACTSOURCETYPE_RENT_AREA.getCode().equals(vo.getContractSourceType())) {
                //租金标准/面积变更
                chageType = PaymentEnums.GY_RENT_AREA.getCode();

                if (PaymentEnums.TYPE_OF_CONTRACT_LOOSE.getCode().equals(vo.getContractType())) {
                    //合同中心传递的--产品信息列表
                    List<BbctPushProductInfoVo> productInfoList = vo.getProductInfoList();
                    BbctPushProductInfoVo bbctPushProductInfoVo = getBbctPushProductInfo(productInfoList, 0);
                    //合同中心传递的--签约方信息
                    List<BbctPushSignatoryInfoVo> signatoryInfoList = vo.getSignatoryInfoList();
                    //从产品信息列表里面取--兼容--
                    if(signatoryInfoList == null || signatoryInfoList.size() == 0){
                        signatoryInfoList = bbctPushProductInfoVo.getSignatoryInfoList();
                    }
                    BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(signatoryInfoList, 0);

                    //3	chargeSubjectList	计费科目列表  租金和押金
                    ChargeSubjectParamsRequest chargeSubjecParamsRequestOne = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_ONE.getCode());
                    ChargeSubjectParamsRequest chargeSubjecParamsRequestTwo = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode());

                    List<ChargeSubjectParamsRequest> chargeSubjectParamsRequestList = new ArrayList<>();
                    List<BbctPushChargeSubjectVo> furnitureSubjectVos = bbctPushProductInfoVo.getChargeSubjectList().stream()
                            .filter(element ->  PaymentEnums.CHARGE_SUBJECT_NO_THREE.getCode().equals(element.getChargeSubjectNo()))
                            .collect(Collectors.toList());
                    ChargeSubjectParamsRequest chargeSubjecParamsRequestThree = null;
                    if(furnitureSubjectVos!=null && furnitureSubjectVos.size() > 0){
                        chargeSubjecParamsRequestThree = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_THREE.getCode());
                    }
                    if(chargeSubjecParamsRequestOne != null && StringUtils.isNotBlank(chargeSubjecParamsRequestOne.getChargeSubjectNo())){
                        chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestOne);
                    }
                    if(chargeSubjecParamsRequestTwo != null && StringUtils.isNotBlank(chargeSubjecParamsRequestTwo.getChargeSubjectNo())){
                        chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestTwo);
                    }
                    if(chargeSubjecParamsRequestThree != null && StringUtils.isNotBlank(chargeSubjecParamsRequestThree.getChargeSubjectNo())){
                        chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestThree);
                    }

                    //家具
                    List<BbctFurnitureRentalVo> furnitureRentalList = bbctPushProductInfoVo.getFurnitureRentalList();
                    List<FurnitureRentalParamsRequest> furnitureRentalParamsRequestList = new ArrayList<>();
                    if (furnitureRentalList != null && furnitureRentalList.size() > 0) {
                        for(BbctFurnitureRentalVo bbctFurnitureRentalVo : furnitureRentalList){
                            FurnitureRentalParamsRequest furnitureRentalParamsRequest = FurnitureRentalParamsRequest.builder()
                                    .furnitureId(bbctFurnitureRentalVo.getFurnitureId())
                                    .furnitureName(bbctFurnitureRentalVo.getFurnitureName())
                                    .furnitureRental(bbctFurnitureRentalVo.getFurnitureRental())
                                    .build();
                            furnitureRentalParamsRequestList.add(furnitureRentalParamsRequest);
                        }
                    } else {
                        furnitureRentalParamsRequestList = null;
                    }

                    bbpmMainLesseeExcelVo.setContractNo(vo.getContractId());

                    //构造请求实体类
                    ContractChangeRentAreaDto  contractChangeRentAreaDTO = ContractChangeRentAreaDto.builder()
                            .requestDate(vo.getRequestDate())
                            .changeAccountingPeriodType(vo.getChangeAccountingPeriodType())
                            .agreementCode(vo.getContractId())
                            .contractType(vo.getContractType())
                            .contractCode(vo.getParentContractCode())
                            .parentContractCode(vo.getRelationContractCode())
                            .houseId(bbctPushProductInfoVo.getHouseId())
                            .chargeSubjectList(chargeSubjectParamsRequestList)
//                            .furnitureRentalList(furnitureRentalParamsRequestList)
                            .build();

                    contractChangeRentAreaDTO.setFurnitureRentalList(furnitureRentalParamsRequestList);

                    ChangeContractRequest changeContractRequest = ChangeContractRequest.builder()
                            .changeType(chageType)
                            .projectId(bbctPushProductInfoVo.getProjectId())
                            .contractChangeRentAreaDTO(contractChangeRentAreaDTO)
                            .build();
                    parentRequest.setData(changeContractRequest);

                } else if(PaymentEnums.TYPE_OF_CONTRACT_MANAGER.getCode().equals(vo.getContractType())){
                    //管理协议 --
                    //合同中心传递的--产品信息列表
                    List<BbctPushProductInfoVo> productInfoList = vo.getProductInfoList();
                    BbctPushProductInfoVo bbctPushProductInfoVo = getBbctPushProductInfo(productInfoList, 0);
                    //合同中心传递的--签约方信息
                    List<BbctPushSignatoryInfoVo> signatoryInfoList = vo.getSignatoryInfoList();
                    //从产品信息列表里面取--兼容--
                    if(signatoryInfoList == null || signatoryInfoList.size() == 0){
                        signatoryInfoList = bbctPushProductInfoVo.getSignatoryInfoList();
                    }

                    BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(signatoryInfoList, 0);
                    BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo2 = getBbctPushSignatoryInfo(signatoryInfoList, 1);
                    BbctPushSignatoryInfoVo enterpriseSignatoryInfoVo = new BbctPushSignatoryInfoVo();
                    BbctPushSignatoryInfoVo personSignatoryInfoVo = new BbctPushSignatoryInfoVo();
                    //找到企业、个人
                    if(bbctPushSignatoryInfoVo2 == null){
                        //看3.3散租接口是赋值在企业上的，不清楚之前逻辑了
                        enterpriseSignatoryInfoVo = bbctPushSignatoryInfoVo;
                    }else{
                        if(PaymentEnums.SIGNATORYTYPE_ENTERPRISE.getCode().equals(bbctPushSignatoryInfoVo.getSignatoryType())){
                            enterpriseSignatoryInfoVo = bbctPushSignatoryInfoVo;
                            personSignatoryInfoVo = bbctPushSignatoryInfoVo2;
                        }else{
                            enterpriseSignatoryInfoVo = bbctPushSignatoryInfoVo2;
                            personSignatoryInfoVo = bbctPushSignatoryInfoVo;
                        }
                    }

                    //3	chargeSubjectList	计费科目列表  租金和押金
                    ChargeSubjectParamsRequest chargeSubjecParamsRequestOne = getChargeSubject(vo,enterpriseSignatoryInfoVo,personSignatoryInfoVo,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_ONE.getCode());
                    ChargeSubjectParamsRequest chargeSubjecParamsRequestTwo = getChargeSubject(vo,enterpriseSignatoryInfoVo,personSignatoryInfoVo,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode());

                    List<ChargeSubjectParamsRequest> chargeSubjectParamsRequestList = new ArrayList<>();
                    List<BbctPushChargeSubjectVo> furnitureSubjectVos = bbctPushProductInfoVo.getChargeSubjectList().stream()
                            .filter(element ->  PaymentEnums.CHARGE_SUBJECT_NO_THREE.getCode().equals(element.getChargeSubjectNo()))
                            .collect(Collectors.toList());
                    ChargeSubjectParamsRequest chargeSubjecParamsRequestThree = null;
                    if(furnitureSubjectVos!=null && furnitureSubjectVos.size() > 0){
                        chargeSubjecParamsRequestThree = getChargeSubject(vo,enterpriseSignatoryInfoVo,personSignatoryInfoVo,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_THREE.getCode());
                    }
                    if(chargeSubjecParamsRequestOne != null && StringUtils.isNotBlank(chargeSubjecParamsRequestOne.getChargeSubjectNo())){
                        chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestOne);
                    }
                    if(chargeSubjecParamsRequestTwo != null && StringUtils.isNotBlank(chargeSubjecParamsRequestTwo.getChargeSubjectNo())){
                        chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestTwo);
                    }
                    if(chargeSubjecParamsRequestThree != null && StringUtils.isNotBlank(chargeSubjecParamsRequestThree.getChargeSubjectNo())){
                        chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestThree);
                    }

                    //家具
                    List<BbctFurnitureRentalVo> furnitureRentalList = bbctPushProductInfoVo.getFurnitureRentalList();
                    List<FurnitureRentalParamsRequest> furnitureRentalParamsRequestList = new ArrayList<>();
                    if (furnitureRentalList != null && furnitureRentalList.size() > 0) {
                        for(BbctFurnitureRentalVo bbctFurnitureRentalVo : furnitureRentalList){
                            FurnitureRentalParamsRequest furnitureRentalParamsRequest = FurnitureRentalParamsRequest.builder()
                                    .furnitureId(bbctFurnitureRentalVo.getFurnitureId())
                                    .furnitureName(bbctFurnitureRentalVo.getFurnitureName())
                                    .furnitureRental(bbctFurnitureRentalVo.getFurnitureRental())
                                    .build();
                            furnitureRentalParamsRequestList.add(furnitureRentalParamsRequest);
                        }
                    } else {
                        furnitureRentalParamsRequestList = null;
                    }


                    bbpmMainLesseeExcelVo.setContractNo(vo.getContractId());

                    //构造请求实体类
                    ContractChangeRentAreaDto  contractChangeRentAreaDTO = ContractChangeRentAreaDto.builder()
                            .requestDate(vo.getRequestDate())
                            .changeAccountingPeriodType(vo.getChangeAccountingPeriodType())
                            .agreementCode(vo.getContractId())
                            .contractType(vo.getContractType())
                            .contractCode(vo.getParentContractCode())
                            .parentContractCode(vo.getRelationContractCode())
                            .houseId(bbctPushProductInfoVo.getHouseId())
                            .chargeSubjectList(chargeSubjectParamsRequestList)
//                            .furnitureRentalList(furnitureRentalParamsRequestList)
                            .build();

                    contractChangeRentAreaDTO.setFurnitureRentalList(furnitureRentalParamsRequestList);

                    ChangeContractRequest changeContractRequest = ChangeContractRequest.builder()
                            .changeType(chageType)
                            .projectId(bbctPushProductInfoVo.getProjectId())
                            .contractChangeRentAreaDTO(contractChangeRentAreaDTO)
                            .build();
                    parentRequest.setData(changeContractRequest);

                }  else {
                    throw new McpException("只支持散租和管理协议变更");
                }
            } else if (PaymentEnums.CONTRACTSOURCETYPE_PAYMENT_CYCLE_CHANGE.getCode().equals(vo.getContractSourceType())) {
                chageType = PaymentEnums.GY_PAYMENT_CYCLE_CHANGE.getCode();
                ContractChangePeriodDTO contractChangePeriodDTO = new ContractChangePeriodDTO();
                contractChangePeriodDTO.setContractCode(vo.getParentContractCode());
                contractChangePeriodDTO.setAgreementCode(vo.getContractId());
                contractChangePeriodDTO.setChargeSubjectPeriod(vo.getContractPricePeriod());
                contractChangePeriodDTO.setChangeBillStartDate(vo.getChangeBillStartDate());
                contractChangePeriodDTO.setChangeBillEndDate(vo.getChangeBillEndDate());
                List<BbctPushProductInfoVo> productInfoList = vo.getProductInfoList();
                List<RoomInfoVo> roomList = new ArrayList<>();
                for (BbctPushProductInfoVo productInfoVo : productInfoList) {
                    RoomInfoVo roomInfoVo = new RoomInfoVo();
                    roomInfoVo.setHouseId(productInfoVo.getHouseId());
                    List<ChargeSubjectParamsRequest> chargeSubjectList = new ArrayList<>();
                    ChargeSubjectParamsRequest chargeSubjecParamsRequestOne = null;
                    ChargeSubjectParamsRequest chargeSubjecParamsRequestTwo = null;
                    if(PaymentEnums.TYPE_OF_CONTRACT_MANAGER.getCode().equals(vo.getContractType())) {
                        BbctPushSignatoryInfoVo person = null;
                        BbctPushSignatoryInfoVo company = null;
                        BbctPushSignatoryInfoVo first = getBbctPushSignatoryInfo(productInfoVo.getSignatoryInfoList(), 0);
                        BbctPushSignatoryInfoVo second = getBbctPushSignatoryInfo(productInfoVo.getSignatoryInfoList(), 1);
                        if (PaymentEnums.SIGNATORYTYPE_ENTERPRISE.getCode().equals(first.getSignatoryType())) {
                            company = first;
                            person = second;
                        } else {
                            company = second;
                            person = first;
                        }
                        chargeSubjecParamsRequestOne = getChargeSubject(vo,company,person,productInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_ONE.getCode());
                        chargeSubjecParamsRequestTwo = getChargeSubject(vo,company,person,productInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode());
                    } else {
                        BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(productInfoVo.getSignatoryInfoList(), 0);
                        chargeSubjecParamsRequestOne = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,productInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_ONE.getCode());
                        chargeSubjecParamsRequestTwo = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,productInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode());
                    }
                    chargeSubjectList.add(chargeSubjecParamsRequestOne);
                    chargeSubjectList.add(chargeSubjecParamsRequestTwo);
                    roomInfoVo.setChargeSubjectList(chargeSubjectList);
                    roomList.add(roomInfoVo);
                }
                contractChangePeriodDTO.setRoomList(roomList);
                ChangeContractRequest changeContractRequest = ChangeContractRequest.builder()
                        .changeType(chageType)
                        .projectId(productInfoList.get(0).getProjectId())
                        .contractChangePeriodDTO(contractChangePeriodDTO)
                        .build();
                parentRequest.setData(changeContractRequest);
            }
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.setTimeZone(TimeZone.getDefault());
            log.info("3.5.合同变更单据更新接口请求工银参数:" + parentRequest.toString());
            String jsonRequest = objectMapper.writeValueAsString(parentRequest);
            log.info("合同contractId"+bbpmMainLesseeExcelVo.getContractNo()+",3.5.合同变更单据更新接口请求工银参数json:" + jsonRequest);

            bbpmMainLesseeExcelVo.setBankBody(jsonRequest);

            //工银接口暂无
            String responseBody = null;
            if (yecaiFeign) {
                responseBody = bfipChargeFeignClient.updateByContract(parentRequest);
            } else {
                responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl + "/charge/v1/bill/updateByContract", parentRequest);
            }
            log.info("合同contractId"+bbpmMainLesseeExcelVo.getContractNo()+",调用工银3.5.合同变更单据更新接口返回结果为:" + responseBody);

            bbpmMainLesseeExcelVo.setErrorMsg(responseBody);

            FaceMdMapResult faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResult.class);

            if (!("00000").equals(faceMdMapResult.getCode())) {
                log.error("合同contractId"+bbpmMainLesseeExcelVo.getContractNo()+",调用工银3.5.合同变更单据更新接口失败:" + responseBody);
                throw new McpException("*提示:"+faceMdMapResult.getCode()+faceMdMapResult.getMessage());
            }

            return faceMdMapResult.getCode();
        } catch (Exception e) {
            e.printStackTrace();
            bbpmMainLesseeExcelVo.setErrorMsg(formatStackTrace(e));
            throw new McpException(e.getMessage());
        } finally {
            boolean isInsertDb = nacosValueConfig.isInsertDb();
            log.info("合同变更插入标志:"+isInsertDb);
            if(isInsertDb){
                iBbpmMainLesseeExcelService.insertRecord(bbpmMainLesseeExcelVo);
            }
        }
    }

    /**
     * 商业合同变更
     * @param vo
     * @return
     */
    @Override
    public String businessContractChange(ChangeContractBusinessRequest vo) {

        if (vo == null || vo.getBusinessContractChangeOwnerDTO() == null) {
            throw new McpException("传入参数为空");
        }

        BbpmMainLesseeExcelVo bbpmMainLesseeExcelVo = new BbpmMainLesseeExcelVo();
        try {

            String traceId = (String) SystemContextHolder.getCurrentContext().get("traceId");
            bbpmMainLesseeExcelVo.setTraceId(traceId);
            ObjectMapper objm = new ObjectMapper();
            objm.setTimeZone(TimeZone.getDefault());
            bbpmMainLesseeExcelVo.setReqBody(objm.writeValueAsString(vo));

            //启动备用字段fields1  设置以前合同编号
            bbpmMainLesseeExcelVo.setFields1(vo.getBusinessContractChangeOwnerDTO().getContractCode());
            bbpmMainLesseeExcelVo.setContractNo(vo.getBusinessContractChangeOwnerDTO().getContractCode());

            //请求实体
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            ParentRequest<ChangeContractBusinessRequest> parentRequest = new ParentRequest<>();
            parentRequest.setTime(sdf.format(new Date()));
            parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
            parentRequest.setData(vo);


            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.setTimeZone(TimeZone.getDefault());
            log.info("商业--3.5.合同变更单据更新接口请求工银参数:" + parentRequest.toString());
            String jsonRequest = objectMapper.writeValueAsString(parentRequest);
            log.info("合同contractId"+bbpmMainLesseeExcelVo.getContractNo()+",商业--3.5.合同变更单据更新接口请求工银参数json:" + jsonRequest);

            bbpmMainLesseeExcelVo.setBankBody(jsonRequest);

            //工银接口暂无
            String responseBody = null;
            if (yecaiFeign) {
                responseBody = bfipChargeFeignClient.updateByContract(parentRequest);
            } else {
                responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl + "/charge/v1/bill/updateByContract", parentRequest);
            }
            log.info("合同contractId"+bbpmMainLesseeExcelVo.getContractNo()+",调用工银商业--3.5.合同变更单据更新接口返回结果为:" + responseBody);

            bbpmMainLesseeExcelVo.setErrorMsg(responseBody);

            FaceMdMapResult faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResult.class);

            if (!("00000").equals(faceMdMapResult.getCode())) {
                log.error("合同contractId"+bbpmMainLesseeExcelVo.getContractNo()+",调用工银商业--3.5.合同变更单据更新接口失败:" + responseBody);
                throw new McpException("*提示:"+faceMdMapResult.getCode()+faceMdMapResult.getMessage());
            }

            return faceMdMapResult.getCode();
        } catch (Exception e) {
            e.printStackTrace();
            bbpmMainLesseeExcelVo.setErrorMsg(formatStackTrace(e));
            throw new McpException(e.getMessage());
        } finally {
            boolean isInsertDb = nacosValueConfig.isInsertDb();
            if(isInsertDb){
                iBbpmMainLesseeExcelService.insertRecord(bbpmMainLesseeExcelVo);
            }
        }
    }


    /**
     * businessContractChangePaymentDateDTO(商业账单缴费日变更)
     * @param vo
     * @return
     */
    @Override
    public String businessContractChangePaymentDate(ChangeContractPaymentDateRequest vo) {
        if (vo == null || vo.getBusinessContractChangePaymentDateDTO() == null) {
            throw new McpException("传入参数为空");
        }

        BbpmMainLesseeExcelVo bbpmMainLesseeExcelVo = new BbpmMainLesseeExcelVo();
        try {

            String traceId = (String) SystemContextHolder.getCurrentContext().get("traceId");
            bbpmMainLesseeExcelVo.setTraceId(traceId);
            ObjectMapper objm = new ObjectMapper();
            objm.setTimeZone(TimeZone.getDefault());
            bbpmMainLesseeExcelVo.setReqBody(objm.writeValueAsString(vo));

            //启动备用字段fields1  设置以前合同编号
            bbpmMainLesseeExcelVo.setFields1(vo.getBusinessContractChangePaymentDateDTO().getContractCode());
            bbpmMainLesseeExcelVo.setContractNo(vo.getBusinessContractChangePaymentDateDTO().getContractCode());

            //请求实体
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            ParentRequest<ChangeContractPaymentDateRequest> parentRequest = new ParentRequest<>();
            parentRequest.setTime(sdf.format(new Date()));
            parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
            parentRequest.setData(vo);


            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.setTimeZone(TimeZone.getDefault());
            log.info("商业账单缴费日变更--3.5.合同变更单据更新接口请求工银参数:" + parentRequest.toString());
            String jsonRequest = objectMapper.writeValueAsString(parentRequest);
            log.info("合同contractId"+bbpmMainLesseeExcelVo.getContractNo()+",商业账单缴费日变更--3.5.合同变更单据更新接口请求工银参数json:" + jsonRequest);

            bbpmMainLesseeExcelVo.setBankBody(jsonRequest);

            //工银接口暂无
            String responseBody = null;
            if (yecaiFeign) {
                responseBody = bfipChargeFeignClient.updateByContract(parentRequest);
            } else {
                responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl + "/charge/v1/bill/updateByContract", parentRequest);
            }
            log.info("合同contractId"+bbpmMainLesseeExcelVo.getContractNo()+",调用工银商业账单缴费日变更--3.5.合同变更单据更新接口返回结果为:" + responseBody);

            bbpmMainLesseeExcelVo.setErrorMsg(responseBody);

            FaceMdMapResult faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResult.class);

            if (!("00000").equals(faceMdMapResult.getCode())) {
                log.error("合同contractId"+bbpmMainLesseeExcelVo.getContractNo()+",调用工银商业账单缴费日变更--3.5.合同变更单据更新接口失败:" + responseBody);
                throw new McpException("*提示:"+faceMdMapResult.getCode()+faceMdMapResult.getMessage());
            }

            return faceMdMapResult.getCode();
        } catch (Exception e) {
            e.printStackTrace();
            bbpmMainLesseeExcelVo.setErrorMsg(formatStackTrace(e));
            throw new McpException(e.getMessage());
        } finally {
            boolean isInsertDb = nacosValueConfig.isInsertDb();
            if(isInsertDb){
                iBbpmMainLesseeExcelService.insertRecord(bbpmMainLesseeExcelVo);
            }
        }
    }

    /**
     * businessContractChangeStartDTO(商业起租日变更)
     * @param vo
     * @return
     */
    @Override
    public String businessContractChangeStart(ChangeContractStartEndDateRequest vo) {
        if (vo == null || vo.getBusinessContractChangeStartDTO() == null) {
            throw new McpException("传入参数为空");
        }

        BbpmMainLesseeExcelVo bbpmMainLesseeExcelVo = new BbpmMainLesseeExcelVo();
        try {

            String traceId = (String) SystemContextHolder.getCurrentContext().get("traceId");
            bbpmMainLesseeExcelVo.setTraceId(traceId);
            ObjectMapper objm = new ObjectMapper();
            objm.setTimeZone(TimeZone.getDefault());
            bbpmMainLesseeExcelVo.setReqBody(objm.writeValueAsString(vo));

            //启动备用字段fields1  设置以前合同编号
            bbpmMainLesseeExcelVo.setFields1(vo.getBusinessContractChangeStartDTO().getContractCode());
            bbpmMainLesseeExcelVo.setContractNo(vo.getBusinessContractChangeStartDTO().getContractCode());

            //请求实体
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            ParentRequest<ChangeContractStartEndDateRequest> parentRequest = new ParentRequest<>();
            parentRequest.setTime(sdf.format(new Date()));
            parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
            parentRequest.setData(vo);


            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.setTimeZone(TimeZone.getDefault());
            log.info("商业起租日变更--3.5.合同变更单据更新接口请求工银参数:" + parentRequest.toString());
            String jsonRequest = objectMapper.writeValueAsString(parentRequest);
            log.info("合同contractId"+bbpmMainLesseeExcelVo.getContractNo()+",商业起租日变更--3.5.合同变更单据更新接口请求工银参数json:" + jsonRequest);

            bbpmMainLesseeExcelVo.setBankBody(jsonRequest);

            //工银接口暂无
            String responseBody = null;
            if (yecaiFeign) {
                responseBody = bfipChargeFeignClient.updateByContract(parentRequest);
            } else {
                responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl + "/charge/v1/bill/updateByContract", parentRequest);
            }
            log.info("合同contractId"+bbpmMainLesseeExcelVo.getContractNo()+",调用工银商业起租日变更--3.5.合同变更单据更新接口返回结果为:" + responseBody);

            bbpmMainLesseeExcelVo.setErrorMsg(responseBody);

            FaceMdMapResult faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResult.class);

            if (!("00000").equals(faceMdMapResult.getCode())) {
                log.error("合同contractId"+bbpmMainLesseeExcelVo.getContractNo()+",调用工银商业起租日变更--3.5.合同变更单据更新接口失败:" + responseBody);
                throw new McpException("*提示:"+faceMdMapResult.getCode()+faceMdMapResult.getMessage());
            }

            return faceMdMapResult.getCode();
        } catch (Exception e) {
            e.printStackTrace();
            bbpmMainLesseeExcelVo.setErrorMsg(formatStackTrace(e));
            throw new McpException(e.getMessage());
        } finally {
            boolean isInsertDb = nacosValueConfig.isInsertDb();
            if(isInsertDb){
                iBbpmMainLesseeExcelService.insertRecord(bbpmMainLesseeExcelVo);
            }
        }
    }


    /**
     * businessContractChange091011(09商业账单缴费日期变更，10商业合同起租日变更，11商业合同租金标准、缩租)
     * @param vo
     * @return
     */
    @Override
    public String businessContractChange091011(ChangeContractRequestV2 vo) {
        String contractCode ="";
        if(vo.getBusinessContractChangeStartDTO()!=null){
            contractCode = vo.getBusinessContractChangeStartDTO().getContractCode();
        } else if(vo.getBusinessContractChangePaymentDateDTO()!= null) {
            contractCode = vo.getBusinessContractChangePaymentDateDTO().getContractCode();
        }else if(vo.getBusinessContractChangeAmountDTO()!= null){
            contractCode = vo.getBusinessContractChangeAmountDTO().getContractCode();
        }else if(vo.getBusinessChangeSecTypeDTO()!= null){
            contractCode = vo.getBusinessChangeSecTypeDTO().getContractCode();
        }else if(vo.getContractChangeRentFreeDTO()!= null){
            contractCode = vo.getContractChangeRentFreeDTO().getContractCode();
        }else if(vo.getContractChangePeriodDTO()!= null){
            contractCode = vo.getContractChangePeriodDTO().getContractCode();
        }

        BbpmMainLesseeExcelVo bbpmMainLesseeExcelVo = new BbpmMainLesseeExcelVo();
        try {

            String traceId = (String) SystemContextHolder.getCurrentContext().get("traceId");
            bbpmMainLesseeExcelVo.setTraceId(traceId);
            ObjectMapper objm = new ObjectMapper();
            objm.setTimeZone(TimeZone.getDefault());
            bbpmMainLesseeExcelVo.setReqBody(objm.writeValueAsString(vo));

            //启动备用字段fields1  设置以前合同编号
            bbpmMainLesseeExcelVo.setFields1(contractCode);
            bbpmMainLesseeExcelVo.setContractNo(contractCode);

//            ChangeContractRequestV3 vo3 = new ChangeContractRequestV3();
//            vo3.setChangeType(Collections.singletonList(vo.getChangeType()));
//            vo3.setProjectId(vo.getProjectId());
//            if(vo.getBusinessContractChangeStartDTO()!=null){
//                vo3.setBusinessContractChangeStartDTO(vo.getBusinessContractChangeStartDTO());
//            }
//            if(vo.getBusinessContractChangePaymentDateDTO()!= null) {
//                vo3.setBusinessContractChangePaymentDateDTO(vo.getBusinessContractChangePaymentDateDTO());
//            }
//            if(vo.getBusinessContractChangeAmountDTO()!= null){
//               vo3.setBusinessContractChangeAmountDTO(vo.getBusinessContractChangeAmountDTO());
//            }

            //请求实体
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            ParentRequest<ChangeContractRequestV2> parentRequest = new ParentRequest<>();
            parentRequest.setTime(sdf.format(new Date()));
            parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
            parentRequest.setData(vo);


            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.setTimeZone(TimeZone.getDefault());
            log.info("缴费日期，商业起租日变更，租金标准、缩租--3.5.合同变更单据更新接口请求工银参数:" + parentRequest.toString());
            String jsonRequest = objectMapper.writeValueAsString(parentRequest);
            log.info("合同contractId"+bbpmMainLesseeExcelVo.getContractNo()+",缴费日期，商业起租日变更，租金标准、缩租--3.5.合同变更单据更新接口请求工银参数json:" + jsonRequest);

            bbpmMainLesseeExcelVo.setBankBody(jsonRequest);

            //工银接口暂无
            String responseBody = null;
            if (yecaiFeign) {
                responseBody = bfipChargeFeignClient.updateByContract(parentRequest);
                //ParentRequest<PreviewBillsParamsVo> parentRequest1 = new ParentRequest<PreviewBillsParamsVo>();
                //responseBody = bfipChargeFeignClient.getPreviewBillsString(parentRequest1);
            } else {
                responseBody = new RestTemplateUtil<ParentRequest>().post(yecaiUrl + "/charge/v1/bill/updateByContract", parentRequest);
            }
            log.info("合同contractId"+bbpmMainLesseeExcelVo.getContractNo()+",调用工银缴费日期，商业起租日变更，租金标准、缩租--3.5.合同变更单据更新接口返回结果为:" + responseBody);

            bbpmMainLesseeExcelVo.setErrorMsg(responseBody);

            FaceMdMapResult faceMdMapResult = JSON.toJavaObject(JSONObject.parseObject(responseBody), FaceMdMapResult.class);

            if (!("00000").equals(faceMdMapResult.getCode())) {
                log.error("合同contractId"+bbpmMainLesseeExcelVo.getContractNo()+",调用工银缴费日期，商业起租日变更，租金标准、缩租--3.5.合同变更单据更新接口失败:" + responseBody);
                throw new McpException("*提示:"+faceMdMapResult.getCode()+faceMdMapResult.getMessage());
            }

            return faceMdMapResult.getCode();
        } catch (Exception e) {
            e.printStackTrace();
            bbpmMainLesseeExcelVo.setErrorMsg(formatStackTrace(e));
            throw new McpException(e.getMessage());
        } finally {
            boolean isInsertDb = nacosValueConfig.isInsertDb();
            if(isInsertDb){
                iBbpmMainLesseeExcelService.insertRecord(bbpmMainLesseeExcelVo);
            }
        }
    }

    @Override
    public List<ChangeTrialPreviewBillsResultVo> changeTrialPreviewBills(BbctPushInfoVo vo) {
        return baseService.changeTrial(vo);
    }


    /**
     * 对接工银  3.64.合同变更试算接口
     */
    @Override
    public List<ChangeTrialPreviewBillsResultVo> changeTrial(BbctPushInfoVo vo) {
        {
            BbpmMainLesseeExcelVo bbpmMainLesseeExcelVo = new BbpmMainLesseeExcelVo();
            try {
                String traceId = (String) SystemContextHolder.getCurrentContext().get("traceId");
                bbpmMainLesseeExcelVo.setTraceId(traceId);
                ObjectMapper objm = new ObjectMapper();
                objm.setTimeZone(TimeZone.getDefault());
                bbpmMainLesseeExcelVo.setReqBody(objm.writeValueAsString(vo));
                //启动备用字段fields1  设置以前合同编号
                bbpmMainLesseeExcelVo.setFields1(vo.getParentContractCode());
                //请求实体
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
                ParentRequest<ChangeContractRequest> parentRequest = new ParentRequest<>();
                parentRequest.setTime(sdf.format(new Date()));
                parentRequest.setTraceId(UUID.randomUUID().toString().replace("-", ""));
                //设置变更类型
                String chageType = "";
                if (PaymentEnums.CONTRACTSOURCETYPE_PAYMENT_CYCLE_CHANGE.getCode().equals(vo.getContractSourceType())) {
                    chageType = PaymentEnums.GY_PAYMENT_CYCLE_CHANGE.getCode();
                    ContractChangePeriodDTO contractChangePeriodDTO = new ContractChangePeriodDTO();
                    contractChangePeriodDTO.setContractCode(vo.getParentContractCode());
                    contractChangePeriodDTO.setAgreementCode(vo.getContractId());
                    contractChangePeriodDTO.setChargeSubjectPeriod(vo.getContractPricePeriod());
                    contractChangePeriodDTO.setChangeBillStartDate(vo.getChangeBillStartDate());
                    contractChangePeriodDTO.setChangeBillEndDate(vo.getChangeBillEndDate());
                    List<BbctPushProductInfoVo> productInfoList = vo.getProductInfoList();
                    List<RoomInfoVo> roomList = new ArrayList<>();
                    for (BbctPushProductInfoVo productInfoVo : productInfoList) {
                        RoomInfoVo roomInfoVo = new RoomInfoVo();
                        roomInfoVo.setHouseId(productInfoVo.getHouseId());
                        List<ChargeSubjectParamsRequest> chargeSubjectList = new ArrayList<>();
                        ChargeSubjectParamsRequest chargeSubjecParamsRequestOne = null;
                        ChargeSubjectParamsRequest chargeSubjecParamsRequestTwo = null;
                        if(PaymentEnums.TYPE_OF_CONTRACT_MANAGER.getCode().equals(vo.getContractType())) {
                            BbctPushSignatoryInfoVo person = null;
                            BbctPushSignatoryInfoVo company = null;
                            BbctPushSignatoryInfoVo first = getBbctPushSignatoryInfo(productInfoVo.getSignatoryInfoList(), 0);
                            BbctPushSignatoryInfoVo second = getBbctPushSignatoryInfo(productInfoVo.getSignatoryInfoList(), 1);
                            if (PaymentEnums.SIGNATORYTYPE_ENTERPRISE.getCode().equals(first.getSignatoryType())) {
                                company = first;
                                person = second;
                            } else {
                                company = second;
                                person = first;
                            }
                            chargeSubjecParamsRequestOne = getChargeSubject(vo,company,person,productInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_ONE.getCode());
                            chargeSubjecParamsRequestTwo = getChargeSubject(vo,company,person,productInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode());
                        } else {
                            BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(productInfoVo.getSignatoryInfoList(), 0);
                            chargeSubjecParamsRequestOne = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,productInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_ONE.getCode());
                            chargeSubjecParamsRequestTwo = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,productInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode());
                        }
                        chargeSubjectList.add(chargeSubjecParamsRequestOne);
                        chargeSubjectList.add(chargeSubjecParamsRequestTwo);
                        roomInfoVo.setChargeSubjectList(chargeSubjectList);
                        roomList.add(roomInfoVo);
                    }
                    contractChangePeriodDTO.setRoomList(roomList);
                    ChangeContractRequest changeContractRequest = ChangeContractRequest.builder()
                            .changeType(chageType)
                            .projectId(productInfoList.get(0).getProjectId())
                            .contractChangePeriodDTO(contractChangePeriodDTO)
                            .build();
                    parentRequest.setData(changeContractRequest);
                }
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.setTimeZone(TimeZone.getDefault());
                log.info("3.64.合同变更试算接口请求工银参数:" + parentRequest.toString());
                String jsonRequest = objectMapper.writeValueAsString(parentRequest);
                log.info("合同contractId"+bbpmMainLesseeExcelVo.getContractNo()+",3.64.合同变更试算接口请求工银参数json:" + jsonRequest);
                bbpmMainLesseeExcelVo.setBankBody(jsonRequest);
                ChargeRespondVo<List<ChangeTrialPreviewBillsResultVo>> responseBody = null;
                if (yecaiFeign) {
                    responseBody = bfipChargeFeignClient.changeTrial(parentRequest);
                } else {
                    String url = yecaiUrl + "/charge/v1/bill/changeTrial";
                    responseBody = restTemplateUtil.postJsonStringByVo(url, jsonRequest);
                }
                log.info("合同contractId"+bbpmMainLesseeExcelVo.getContractNo()+",调用工银3.64.合同变更试算接口返回结果为:" + responseBody);
                return responseBody.getData();
            } catch (Exception e) {
                e.printStackTrace();
                throw new McpException(e.getMessage());
            }
        }
    }

    /**
     * 获取产品租金
     *
     * @return 产品租金
     */
    private BigDecimal getProductRent(BbctPushChargeSubjectVo bbctPushChargeSubjectVo) {
        ChargeResultParamsVo chargeResultParamsVo = new ChargeResultParamsVo();
        chargeResultParamsVo.setChargeRuleNo(bbctPushChargeSubjectVo.getChargeRuleNo());
        chargeResultParamsVo.setParamValueList(bbctPushChargeSubjectVo.getParamValueList());

        log.info("3.5.合同变更单据更新接口计算租金时请求参数:"+JSONObject.toJSONString(chargeResultParamsVo));
        ChargeRespondVo chargeRespondVo = iBbsiRuleInfoService.getChargeResult(chargeResultParamsVo);
        log.info("3.5.合同变更单据更新接口计算租金时工银返回:"+JSONObject.toJSONString(chargeRespondVo));

        return new BigDecimal(String.valueOf(chargeRespondVo.getData()));
    }

    /**
     * 组装 计费科目相关信息
     *
     * @param chargeSubjectNo
     * @return
     */
    private ChargeSubjectParamsRequest getChargeSubject(BbctPushInfoVo vo,BbctPushSignatoryInfoVo enterpriseSignatoryInfoVo,BbctPushSignatoryInfoVo personSignatoryInfoVo,BbctPushProductInfoVo bbctPushProductInfoVo, String chargeSubjectNo) {
//       //仓储迁移没有这个cao
//        if(PaymentEnums.CONTRACTSOURCETYPE_STORAGE.getCode().equals(vo.getContractSourceType())){
//            return null;
//        }

        //计费科目列表
        List<BbctPushChargeSubjectVo> chargeSubjectList = bbctPushProductInfoVo.getChargeSubjectList();
        if (chargeSubjectList == null || chargeSubjectList.size() == 0) {
            throw new McpException("计费科目列表不能为空");
        }
        //筛选对应科目
        List<BbctPushChargeSubjectVo> bbctPushChargeSubjectVoList = chargeSubjectList.stream()
                .filter(element -> chargeSubjectNo.equals(element.getChargeSubjectNo()))
                .collect(Collectors.toList());
        BbctPushChargeSubjectVo bbctPushChargeSubjectVo = new BbctPushChargeSubjectVo();
        if(bbctPushChargeSubjectVoList!=null && bbctPushChargeSubjectVoList.size()>0){
            bbctPushChargeSubjectVo = bbctPushChargeSubjectVoList.get(0);
        }else {
            return null;
        }

        //保租房-散租、保租房-管理协议、保租房-趸租，签约<新增>功能，通过选择房源数据查取运营项目并获取税率，在<新增-预览>页面的【提交审核】后，需要给计费中心推送税率，计费中心生成账单
        //获取税率
        BigDecimal taxRate = bbctPushChargeSubjectVo.getTaxRate();
        BigDecimal personalTaxRate = bbctPushChargeSubjectVo.getPersonalTaxRate();
        if(PaymentEnums.PROJECTFORMAT_BZ.getCode().equals(bbctPushProductInfoVo.getProjectFormat())
           &&  !PaymentEnums.CHARGE_SUBJECT_NO_THREE.getCode().equals(chargeSubjectNo)){
            switch (vo.getContractSourceType()) {
                case "7":
                case "8":
                case "10":
                case "11":
                case "3":
                    break;
                default:
                    if(taxRate == null){
                        String companyId = null;
                        if(enterpriseSignatoryInfoVo!=null){
                            companyId = enterpriseSignatoryInfoVo.getCompanyId();
                            if(StringUtils.isBlank(companyId) && personSignatoryInfoVo!=null){
                                companyId = personSignatoryInfoVo.getCompanyId();
                            }
                        }
                        TaxRateVo requestVo = new TaxRateVo();
                        requestVo.setProjectId(bbctPushProductInfoVo.getProjectId());
                        requestVo.setCompanyId(companyId);
                        requestVo.setYeTai(PaymentEnums.PROJECTFORMAT_BZ.getCode());
                        requestVo.setChargeItemId(chargeSubjectNo);
                        if (PaymentEnums.TYPE_OF_CONTRACT_LOOSE.getCode().equals(vo.getContractType())) {
                            requestVo.setTenantry(PaymentEnums.TENANTRY_THREE.getCode());
                        } else if (PaymentEnums.TYPE_OF_CONTRACT_SINGLERENT.getCode().equals(vo.getContractType())) {
                            requestVo.setTenantry(PaymentEnums.TENANTRY_TWO.getCode());
                        } else if (PaymentEnums.TYPE_OF_CONTRACT_MANAGER.getCode().equals(vo.getContractType())) {
                            requestVo.setTenantry(PaymentEnums.TENANTRY_ONE.getCode());
                        }
                        log.info("合同contractId"+vo.getContractId()+",请求3.38查询税率配置列表接口传入参数:"+ JSONObject.toJSONString(requestVo));
                        TaxRateResultVo taxRateResultVo = iBbsiRuleInfoService.getTaxRateList(requestVo);
                        if(taxRateResultVo != null){
                            if (PaymentEnums.TYPE_OF_CONTRACT_LOOSE.getCode().equals(vo.getContractType())) {
                                if(StringUtils.isNotBlank(taxRateResultVo.getPersonTaxRate())){
                                    taxRate = percentageStr(taxRateResultVo.getPersonTaxRate());
                                }
                            } else if (PaymentEnums.TYPE_OF_CONTRACT_SINGLERENT.getCode().equals(vo.getContractType())) {
                                if (StringUtils.isNotBlank(taxRateResultVo.getCompanyTaxRate())){
                                    taxRate =  percentageStr(taxRateResultVo.getCompanyTaxRate());
                                }
                            } else if (PaymentEnums.TYPE_OF_CONTRACT_MANAGER.getCode().equals(vo.getContractType())) {
                                if(StringUtils.isNotBlank(taxRateResultVo.getCompanyTaxRate())){
                                    taxRate = percentageStr(taxRateResultVo.getCompanyTaxRate());
                                }
                                if(personalTaxRate==null && StringUtils.isNotBlank(taxRateResultVo.getPersonTaxRate())){
                                    personalTaxRate = percentageStr(taxRateResultVo.getPersonTaxRate());
                                }
                            }
                        }else {
                            log.info("合同contractId"+vo.getContractId()+",请求3.38查询税率配置列表结果为空");
                            if(!PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode().equals(chargeSubjectNo)){
                                throw new McpException("当前运营项目未配置税率，无法进行签约");
                            }
                        }
                    }else{
                        log.info("合同contractId"+vo.getContractId()+",收费科目"+chargeSubjectNo+"税率taxRate="+taxRate+",不用查询工银接口获取税率");
                    }
            }
        }

        List<BbctIncreaseRuleVo> increaseRules = bbctPushChargeSubjectVo.getIncreaseRules();
        List<BbctPreferentRuleVo> preferentRules = bbctPushChargeSubjectVo.getPreferentRules();
        List<IncreaseRulesParamsRequest> increaseRulesParamsRequests = new ArrayList<>();
        List<PreferentRulesParamsRequest> preferentRulesParamsRequests = new ArrayList<>();
        if(increaseRules!=null && increaseRules.size() > 0){
            for (BbctIncreaseRuleVo increaseRuleVo : increaseRules){
                IncreaseRulesParamsRequest increaseRulesParamsRequest = IncreaseRulesParamsRequest.builder()
                        .increaseRuleId(increaseRuleVo.getIncreaseRuleId())
                        .increaseProportion(increaseRuleVo.getIncreaseProportion())
                        .increaseAmount(increaseRuleVo.getIncreaseAmount())
                        .increasePeriod(increaseRuleVo.getIncreasePeriod())
                        .increaseOrder(increaseRuleVo.getIncreaseOrder())
                        .increaseType(increaseRuleVo.getIncreaseType())
                        .build();
                increaseRulesParamsRequests.add(increaseRulesParamsRequest);
            }
        }else {
            increaseRulesParamsRequests = null;
        }
        if(preferentRules!=null && preferentRules.size() > 0){
            for(BbctPreferentRuleVo bbctPreferentRuleVo :preferentRules){
                PreferentRulesParamsRequest preferentRulesParamsRequest = PreferentRulesParamsRequest.builder()
                        .preferentialType(bbctPreferentRuleVo.getPreferentialType())
                        .preferentialBeginDate(bbctPreferentRuleVo.getPreferentialBeginDate())
                        .preferentialEndDate(bbctPreferentRuleVo.getPreferentialEndDate())
                        .preferentialAmount(bbctPreferentRuleVo.getPreferentialAmount())
                        .preferentialRatio(bbctPreferentRuleVo.getPreferentialRatio())
                        .preferentRuleId(bbctPreferentRuleVo.getPreferentRuleId())
                        .build();
                preferentRulesParamsRequests.add(preferentRulesParamsRequest);
            }
        }else {
            preferentRulesParamsRequests = null;
        }

        ChargeRuleSubParamsVo paramsVo = bbctPushChargeSubjectVo.getParamList();
        ChargeRuleSubParamsVo paramValues = bbctPushChargeSubjectVo.getParamValueList();
        JSONObject paramList = null;
        JSONObject paramValueList = null;
        if(paramsVo != null){
            paramList = (JSONObject) JSONObject.toJSON(paramsVo);
        }
        if(paramValues != null){
            paramValueList = (JSONObject) JSONObject.toJSON(paramValues);
        }

        //从费用科目实体里取,以前是在最外面--兼容--
        if(StringUtils.isNotBlank(bbctPushChargeSubjectVo.getShareType())){
            BigDecimal companyRate = enterpriseSignatoryInfoVo.getCompanyRate()!=null?enterpriseSignatoryInfoVo.getCompanyRate().setScale(4,BigDecimal.ROUND_HALF_UP):enterpriseSignatoryInfoVo.getCompanyRate();
            BigDecimal companyAmount = enterpriseSignatoryInfoVo.getCompanyAmount()!=null?enterpriseSignatoryInfoVo.getCompanyAmount().setScale(4,BigDecimal.ROUND_HALF_UP):enterpriseSignatoryInfoVo.getCompanyAmount();
            //押金的时候 趸租和管理协议
            if(PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode().equals(chargeSubjectNo)){
                if(PaymentEnums.TYPE_OF_CONTRACT_SINGLERENT.getCode().equals(vo.getContractType()) || PaymentEnums.TYPE_OF_CONTRACT_MANAGER.getCode().equals(vo.getContractType())){
                    companyRate = enterpriseSignatoryInfoVo.getCompanyCashPledgePercent();
                    companyAmount = enterpriseSignatoryInfoVo.getCompanyCashPledgeAmount();
                }
            }

            ChargeSubjectParamsRequest chargeSubjecParamsRequest = ChargeSubjectParamsRequest.builder()
                    .chargeSubjectNo(bbctPushChargeSubjectVo.getChargeSubjectNo())
                    .chargeSubjectAmount(bbctPushChargeSubjectVo.getChargeSubjectAmount())
                    .amountType(bbctPushChargeSubjectVo.getAmountType())
                    .cyclicOrSingle(bbctPushChargeSubjectVo.getCyclicOrSingle())
                    .chargeSubjectPeriod(bbctPushChargeSubjectVo.getChargeSubjectPeriod())
                    .depositProportion(bbctPushChargeSubjectVo.getDepositProportion())
                    .chargeRuleNo(bbctPushChargeSubjectVo.getChargeRuleNo())
                    .chargeRuleName(bbctPushChargeSubjectVo.getChargeRuleName())
                    .paramList(paramList)
                    .paramValueList(paramValueList)
                    .taxRate(taxRate)
                    .personalTaxRate(personalTaxRate)
                    //---
                    .shareType(bbctPushChargeSubjectVo.getShareType())
                    .companyRate(companyRate)
                    .personalRate(enterpriseSignatoryInfoVo.getPersonalRate()!=null?enterpriseSignatoryInfoVo.getPersonalRate().setScale(4,BigDecimal.ROUND_HALF_UP):enterpriseSignatoryInfoVo.getPersonalRate())
                    .companyAmount(companyAmount)
                    .personalAmount(enterpriseSignatoryInfoVo.getPersonalAmount()!=null?enterpriseSignatoryInfoVo.getPersonalAmount().setScale(4,BigDecimal.ROUND_HALF_UP):enterpriseSignatoryInfoVo.getPersonalAmount())

                    //增值服务费新增字段
                    .hasServiceFee(bbctPushChargeSubjectVo.getHasServiceFee())
                    .serviceFeeChargeRule(bbctPushChargeSubjectVo.getServiceFeeChargeRule())
                    .monthlyRentServiceFeeRatio(bbctPushChargeSubjectVo.getMonthlyRentServiceFeeRatio())
                    .serviceFeePricePerSquareMeter(bbctPushChargeSubjectVo.getServiceFeePricePerSquareMeter())
                    .fixedServiceFee(bbctPushChargeSubjectVo.getFixedServiceFee())
                    .serviceFeeCompanyAmount(bbctPushChargeSubjectVo.getServiceFeeCompanyAmount())
                    .serviceFeeCompanyRate(bbctPushChargeSubjectVo.getServiceFeeCompanyRate())
                    .serviceFeeShareType(bbctPushChargeSubjectVo.getServiceFeeShareType())

                    //停车优惠
                    .preferentialCategory(bbctPushChargeSubjectVo.getPreferentialCategory())
                    .preferentialAmount(bbctPushChargeSubjectVo.getPreferentialAmount())
                    .preferentialRatio(bbctPushChargeSubjectVo.getPreferentialRatio())

                    .build();

            chargeSubjecParamsRequest.setIncreaseRules(increaseRulesParamsRequests);
            chargeSubjecParamsRequest.setPreferentRules(preferentRulesParamsRequests);

            //3.27特殊处理
            if(personSignatoryInfoVo != null){
                chargeSubjecParamsRequest.setPersonalRate(personSignatoryInfoVo.getPersonalRate());
                chargeSubjecParamsRequest.setPersonalAmount(personSignatoryInfoVo.getPersonalAmount());
            }
            return chargeSubjecParamsRequest;
        }else{
            //3.26和3.27 为押金条时companyRate特殊处理
            if(PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode().equals(chargeSubjectNo)
                    && (PaymentEnums.TYPE_OF_CONTRACT_SINGLERENT.getCode().equals(vo.getContractType()))){

                ChargeSubjectParamsRequest chargeSubjecParamsRequest = ChargeSubjectParamsRequest.builder()
                        .chargeSubjectNo(bbctPushChargeSubjectVo.getChargeSubjectNo())
                        .chargeSubjectAmount(bbctPushChargeSubjectVo.getChargeSubjectAmount())
                        .amountType(bbctPushChargeSubjectVo.getAmountType())
                        .cyclicOrSingle(bbctPushChargeSubjectVo.getCyclicOrSingle())
                        .chargeSubjectPeriod(bbctPushChargeSubjectVo.getChargeSubjectPeriod())
                        .depositProportion(bbctPushChargeSubjectVo.getDepositProportion())
                        .chargeRuleNo(bbctPushChargeSubjectVo.getChargeRuleNo())
                        .chargeRuleName(bbctPushChargeSubjectVo.getChargeRuleName())
                        .paramList(paramList)
                        .paramValueList(paramValueList)
                        .taxRate(taxRate)
                        .personalTaxRate(personalTaxRate)
                        //3.26趸租大合同生成账单接口、3.27
                        .shareType("3")
                        .companyRate(new BigDecimal(1).setScale(4,BigDecimal.ROUND_HALF_UP))
                        .personalRate(enterpriseSignatoryInfoVo.getPersonalRate()!=null?enterpriseSignatoryInfoVo.getPersonalRate().setScale(4,BigDecimal.ROUND_HALF_UP):enterpriseSignatoryInfoVo.getPersonalRate())
                        .companyAmount(enterpriseSignatoryInfoVo.getCompanyAmount()!=null?enterpriseSignatoryInfoVo.getCompanyAmount().setScale(4,BigDecimal.ROUND_HALF_UP):enterpriseSignatoryInfoVo.getCompanyAmount())
                        .personalAmount(enterpriseSignatoryInfoVo.getPersonalAmount()!=null?enterpriseSignatoryInfoVo.getPersonalAmount().setScale(4,BigDecimal.ROUND_HALF_UP):enterpriseSignatoryInfoVo.getPersonalAmount())

                        //增值服务费新增字段
                        .hasServiceFee(bbctPushChargeSubjectVo.getHasServiceFee())
                        .serviceFeeChargeRule(bbctPushChargeSubjectVo.getServiceFeeChargeRule())
                        .monthlyRentServiceFeeRatio(bbctPushChargeSubjectVo.getMonthlyRentServiceFeeRatio())
                        .serviceFeePricePerSquareMeter(bbctPushChargeSubjectVo.getServiceFeePricePerSquareMeter())
                        .fixedServiceFee(bbctPushChargeSubjectVo.getFixedServiceFee())
                        .serviceFeeCompanyAmount(bbctPushChargeSubjectVo.getServiceFeeCompanyAmount())
                        .serviceFeeCompanyRate(bbctPushChargeSubjectVo.getServiceFeeCompanyRate())
                        .serviceFeeShareType(bbctPushChargeSubjectVo.getServiceFeeShareType())

                        //停车优惠
                        .preferentialCategory(bbctPushChargeSubjectVo.getPreferentialCategory())
                        .preferentialAmount(bbctPushChargeSubjectVo.getPreferentialAmount())
                        .preferentialRatio(bbctPushChargeSubjectVo.getPreferentialRatio())

                        .build();

                chargeSubjecParamsRequest.setIncreaseRules(increaseRulesParamsRequests);
                chargeSubjecParamsRequest.setPreferentRules(preferentRulesParamsRequests);

                //3.27特殊处理
                if(personSignatoryInfoVo != null){
                    chargeSubjecParamsRequest.setPersonalRate(personSignatoryInfoVo.getPersonalRate());
                    chargeSubjecParamsRequest.setPersonalAmount(personSignatoryInfoVo.getPersonalAmount());
                }

                return chargeSubjecParamsRequest;

            }else if(PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode().equals(chargeSubjectNo)
                    && (PaymentEnums.TYPE_OF_CONTRACT_MANAGER.getCode().equals(vo.getContractType()))){

                //没有的话自己支付,有的话没有押金这个费用科目
                if(StringUtils.isBlank(vo.getRelationContractCode()) ){
                    ChargeSubjectParamsRequest chargeSubjecParamsRequest = ChargeSubjectParamsRequest.builder()
                            .chargeSubjectNo(bbctPushChargeSubjectVo.getChargeSubjectNo())
                            .chargeSubjectAmount(bbctPushChargeSubjectVo.getChargeSubjectAmount())
                            .amountType(bbctPushChargeSubjectVo.getAmountType())
                            .cyclicOrSingle(bbctPushChargeSubjectVo.getCyclicOrSingle())
                            .chargeSubjectPeriod(bbctPushChargeSubjectVo.getChargeSubjectPeriod())
                            .depositProportion(bbctPushChargeSubjectVo.getDepositProportion())
                            .chargeRuleNo(bbctPushChargeSubjectVo.getChargeRuleNo())
                            .chargeRuleName(bbctPushChargeSubjectVo.getChargeRuleName())
                            .paramList(paramList)
                            .paramValueList(paramValueList)
                            .taxRate(taxRate)
                            .personalTaxRate(personalTaxRate)
                            //3.26趸租大合同生成账单接口、3.27
                            .shareType("4")
                            .companyRate(new BigDecimal(0).setScale(4,BigDecimal.ROUND_HALF_UP))
                            .personalRate(new BigDecimal(1).setScale(4,BigDecimal.ROUND_HALF_UP))
                            .companyAmount(enterpriseSignatoryInfoVo.getCompanyAmount()!=null?enterpriseSignatoryInfoVo.getCompanyAmount().setScale(4,BigDecimal.ROUND_HALF_UP):enterpriseSignatoryInfoVo.getCompanyAmount())
                            .personalAmount(enterpriseSignatoryInfoVo.getPersonalAmount()!=null?enterpriseSignatoryInfoVo.getPersonalAmount().setScale(4,BigDecimal.ROUND_HALF_UP):enterpriseSignatoryInfoVo.getPersonalAmount())

                            //增值服务费新增字段
                            .hasServiceFee(bbctPushChargeSubjectVo.getHasServiceFee())
                            .serviceFeeChargeRule(bbctPushChargeSubjectVo.getServiceFeeChargeRule())
                            .monthlyRentServiceFeeRatio(bbctPushChargeSubjectVo.getMonthlyRentServiceFeeRatio())
                            .serviceFeePricePerSquareMeter(bbctPushChargeSubjectVo.getServiceFeePricePerSquareMeter())
                            .fixedServiceFee(bbctPushChargeSubjectVo.getFixedServiceFee())
                            .serviceFeeCompanyAmount(bbctPushChargeSubjectVo.getServiceFeeCompanyAmount())
                            .serviceFeeCompanyRate(bbctPushChargeSubjectVo.getServiceFeeCompanyRate())
                            .serviceFeeShareType(bbctPushChargeSubjectVo.getServiceFeeShareType())

                            //停车优惠
                            .preferentialCategory(bbctPushChargeSubjectVo.getPreferentialCategory())
                            .preferentialAmount(bbctPushChargeSubjectVo.getPreferentialAmount())
                            .preferentialRatio(bbctPushChargeSubjectVo.getPreferentialRatio())

                            .build();

                    chargeSubjecParamsRequest.setIncreaseRules(increaseRulesParamsRequests);
                    chargeSubjecParamsRequest.setPreferentRules(preferentRulesParamsRequests);

                    //3.27特殊处理
                    if(personSignatoryInfoVo != null){
                        chargeSubjecParamsRequest.setPersonalRate(personSignatoryInfoVo.getPersonalRate());
                        chargeSubjecParamsRequest.setPersonalAmount(personSignatoryInfoVo.getPersonalAmount());
                    }

                    return chargeSubjecParamsRequest;
                }else {
                    return null;
                }
            }else{

                ChargeSubjectParamsRequest chargeSubjecParamsRequest = ChargeSubjectParamsRequest.builder()
                        .chargeSubjectNo(bbctPushChargeSubjectVo.getChargeSubjectNo())
                        .chargeSubjectAmount(bbctPushChargeSubjectVo.getChargeSubjectAmount())
                        .amountType(bbctPushChargeSubjectVo.getAmountType())
                        .cyclicOrSingle(bbctPushChargeSubjectVo.getCyclicOrSingle())
                        .chargeSubjectPeriod(bbctPushChargeSubjectVo.getChargeSubjectPeriod())
                        .depositProportion(bbctPushChargeSubjectVo.getDepositProportion())
                        .chargeRuleNo(bbctPushChargeSubjectVo.getChargeRuleNo())
                        .chargeRuleName(bbctPushChargeSubjectVo.getChargeRuleName())
                        .paramList(paramList)
                        .paramValueList(paramValueList)
                        .taxRate(taxRate)
                        .personalTaxRate(personalTaxRate)
                        //3.26趸租大合同生成账单接口、3.27
                        .shareType(vo.getShareType())
                        .companyRate(enterpriseSignatoryInfoVo.getCompanyRate()!=null?enterpriseSignatoryInfoVo.getCompanyRate().setScale(4,BigDecimal.ROUND_HALF_UP):enterpriseSignatoryInfoVo.getCompanyRate())
                        .personalRate(enterpriseSignatoryInfoVo.getPersonalRate()!=null?enterpriseSignatoryInfoVo.getPersonalRate().setScale(4,BigDecimal.ROUND_HALF_UP):enterpriseSignatoryInfoVo.getPersonalRate())
                        .companyAmount(enterpriseSignatoryInfoVo.getCompanyAmount()!=null?enterpriseSignatoryInfoVo.getCompanyAmount().setScale(4,BigDecimal.ROUND_HALF_UP):enterpriseSignatoryInfoVo.getCompanyAmount())
                        .personalAmount(enterpriseSignatoryInfoVo.getPersonalAmount()!=null?enterpriseSignatoryInfoVo.getPersonalAmount().setScale(4,BigDecimal.ROUND_HALF_UP):enterpriseSignatoryInfoVo.getPersonalAmount())

                        //增值服务费新增字段
                        .hasServiceFee(bbctPushChargeSubjectVo.getHasServiceFee())
                        .serviceFeeChargeRule(bbctPushChargeSubjectVo.getServiceFeeChargeRule())
                        .monthlyRentServiceFeeRatio(bbctPushChargeSubjectVo.getMonthlyRentServiceFeeRatio())
                        .serviceFeePricePerSquareMeter(bbctPushChargeSubjectVo.getServiceFeePricePerSquareMeter())
                        .fixedServiceFee(bbctPushChargeSubjectVo.getFixedServiceFee())
                        .serviceFeeCompanyAmount(bbctPushChargeSubjectVo.getServiceFeeCompanyAmount())
                        .serviceFeeCompanyRate(bbctPushChargeSubjectVo.getServiceFeeCompanyRate())
                        .serviceFeeShareType(bbctPushChargeSubjectVo.getServiceFeeShareType())

                        //停车优惠
                        .preferentialCategory(bbctPushChargeSubjectVo.getPreferentialCategory())
                        .preferentialAmount(bbctPushChargeSubjectVo.getPreferentialAmount())
                        .preferentialRatio(bbctPushChargeSubjectVo.getPreferentialRatio())

                        .build();

                chargeSubjecParamsRequest.setIncreaseRules(increaseRulesParamsRequests);
                chargeSubjecParamsRequest.setPreferentRules(preferentRulesParamsRequests);

                //3.27特殊处理
                if(personSignatoryInfoVo != null){
                    chargeSubjecParamsRequest.setPersonalRate(personSignatoryInfoVo.getPersonalRate());
                    chargeSubjecParamsRequest.setPersonalAmount(personSignatoryInfoVo.getPersonalAmount());
                }
                return chargeSubjecParamsRequest;
            }
        }
    }


    /**
     * 对接工银   3.54 仓储续签
     * <p>
     */
    @Override
    public String storageRelet(BbctPushInfoVo vo) {
        //请求实体
        BankRequestVo<BillStorageReletCreateParamsRequest> bankRequestVo = new BankRequestVo<>();

        //合同中心传递的--产品信息列表
        List<BbctPushProductInfoVo> productInfoList = vo.getProductInfoList();
        BbctPushProductInfoVo bbctPushProductInfoVo = getBbctPushProductInfo(productInfoList, 0);
        //合同中心传递的--签约方信息
        List<BbctPushSignatoryInfoVo> signatoryInfoList = vo.getSignatoryInfoList();
        //从产品信息列表里面取--兼容--
        if(signatoryInfoList == null || signatoryInfoList.size() == 0){
            signatoryInfoList = bbctPushProductInfoVo.getSignatoryInfoList();
        }
        BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo = getBbctPushSignatoryInfo(signatoryInfoList, 0);

        //3	chargeSubjectList	计费科目列表  租金和押金
        ChargeSubjectParamsRequest chargeSubjecParamsRequestOne = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_ONE.getCode());
        ChargeSubjectParamsRequest chargeSubjecParamsRequestTwo = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_TWO.getCode());

        List<ChargeSubjectParamsRequest> chargeSubjectParamsRequestList = new ArrayList<>();
        List<BbctPushChargeSubjectVo> furnitureSubjectVos = bbctPushProductInfoVo.getChargeSubjectList().stream()
                .filter(element ->  PaymentEnums.CHARGE_SUBJECT_NO_THREE.getCode().equals(element.getChargeSubjectNo()))
                .collect(Collectors.toList());
        List<BbctPushChargeSubjectVo> carSubjectVos = bbctPushProductInfoVo.getChargeSubjectList().stream()
                .filter(element ->  PaymentEnums.CHARGE_SUBJECT_NO_FOUR.getCode().equals(element.getChargeSubjectNo()))
                .collect(Collectors.toList());
        List<BbctPushChargeSubjectVo> fiveSubjectVos = bbctPushProductInfoVo.getChargeSubjectList().stream()
                .filter(element ->  PaymentEnums.CHARGE_SUBJECT_NO_FIVE.getCode().equals(element.getChargeSubjectNo()))
                .collect(Collectors.toList());
        List<BbctPushChargeSubjectVo> fifteenSubjectVos = bbctPushProductInfoVo.getChargeSubjectList().stream()
                .filter(element ->  PaymentEnums.CHARGE_SUBJECT_NO_FIFTEEN.getCode().equals(element.getChargeSubjectNo()))
                .collect(Collectors.toList());

        ChargeSubjectParamsRequest chargeSubjecParamsRequestThree = null;
        ChargeSubjectParamsRequest chargeSubjecParamsRequestFour = null;
        ChargeSubjectParamsRequest chargeSubjecParamsRequestFive = null;
        ChargeSubjectParamsRequest chargeSubjecParamsRequestFifteen = null;
        if(furnitureSubjectVos!=null && furnitureSubjectVos.size() > 0){
            chargeSubjecParamsRequestThree = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_THREE.getCode());
        }
        if(carSubjectVos!=null && carSubjectVos.size() > 0){
            chargeSubjecParamsRequestFour = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_FOUR.getCode());
        }
        if(fiveSubjectVos!=null && fiveSubjectVos.size() > 0){
            chargeSubjecParamsRequestFive = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_FIVE.getCode());
        }
        if(fifteenSubjectVos!=null && fifteenSubjectVos.size() > 0){
            chargeSubjecParamsRequestFifteen = getChargeSubject(vo,bbctPushSignatoryInfoVo,null,bbctPushProductInfoVo, PaymentEnums.CHARGE_SUBJECT_NO_FIFTEEN.getCode());
        }

        if(chargeSubjecParamsRequestOne != null && StringUtils.isNotBlank(chargeSubjecParamsRequestOne.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestOne);
        }
        if(chargeSubjecParamsRequestTwo != null && StringUtils.isNotBlank(chargeSubjecParamsRequestTwo.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestTwo);
        }
        if(chargeSubjecParamsRequestThree != null && StringUtils.isNotBlank(chargeSubjecParamsRequestThree.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestThree);
        }
        if(chargeSubjecParamsRequestFour != null && StringUtils.isNotBlank(chargeSubjecParamsRequestFour.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestFour);
        }
        if(chargeSubjecParamsRequestFive != null && StringUtils.isNotBlank(chargeSubjecParamsRequestFive.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestFive);
        }
        if(chargeSubjecParamsRequestFifteen != null && StringUtils.isNotBlank(chargeSubjecParamsRequestFifteen.getChargeSubjectNo())){
            chargeSubjectParamsRequestList.add(chargeSubjecParamsRequestFifteen);
        }

        //最终发送对象
        BillStorageReletCreateParamsRequest storageReletCreateParamsRequest = BillStorageReletCreateParamsRequest.builder()
                .originalContractId(vo.getParentContractCode())
                .historyData(vo.getHistoryData())
                .chargeSubjectList(chargeSubjectParamsRequestList)
//                .chargeSubjecParamsRequest(chargeSubjecParamsRequestOne)
//                .chargeSubjecParamsRequest(chargeSubjecParamsRequestTwo)
                //租户
                .tenantBankName(bbctPushSignatoryInfoVo.getTenantBankName())
                .tenantBankCode(bbctPushSignatoryInfoVo.getTenantBankCode())
                .tenantBankAccountName(bbctPushSignatoryInfoVo.getTenantBankAccountName())
                .tenantBankAccountNo(bbctPushSignatoryInfoVo.getTenantBankAccountNo())
                .withholding(bbctPushSignatoryInfoVo.getWithholding())
                .withholdingSummary(PaymentEnums.WITHHOLDING_YES.getCode().equals(bbctPushSignatoryInfoVo.getWithholding()) ? "房租" : null)
                .withholdingRemark(PaymentEnums.WITHHOLDING_YES.getCode().equals(bbctPushSignatoryInfoVo.getWithholding()) ? "3" : null)
                .agreementNo(StringUtils.isNotBlank(bbctPushSignatoryInfoVo.getAgreementNo())? bbctPushSignatoryInfoVo.getAgreementNo() : "0000")
                .tenantName(bbctPushSignatoryInfoVo.getTenantName())
                .tenantMobile(bbctPushSignatoryInfoVo.getTenantMobile())
                .tenantId(bbctPushSignatoryInfoVo.getTenantId())
                .tenantIDType(bbctPushSignatoryInfoVo.getTenantIDType())
                .idNumber(bbctPushSignatoryInfoVo.getIdNumber())
                .mailUrl(bbctPushSignatoryInfoVo.getMailUrl())
                .publicRecordNo(bbctPushSignatoryInfoVo.getPublicRecordNo())
                .tenantCustomerNo(bbctPushSignatoryInfoVo.getTenantCustomerNo())
                .tenantSupplierNo(bbctPushSignatoryInfoVo.getTenantSupplierNo())
                .tenantSupplierName(bbctPushSignatoryInfoVo.getTenantSupplierName())
                //项目
                .projectId(bbctPushProductInfoVo.getProjectId())
                .projectNo(StringUtils.isNotBlank(bbctPushProductInfoVo.getProjectNo()) ? bbctPushProductInfoVo.getProjectNo() : bbctPushProductInfoVo.getProjectId())
                .projectName(bbctPushProductInfoVo.getProjectName())
                .projectShortName(bbctPushProductInfoVo.getProjectShortName())
                .operateEntityType(bbctPushProductInfoVo.getOperateEntityType())
                .operateEntityName(bbctPushProductInfoVo.getOperateEntityName())
                .operateUnitBusinessNo(bbctPushProductInfoVo.getOperateUnitBusinessNo())
                .operateUnitNo(bbctPushProductInfoVo.getOperateUnitNo())
                .operateUnitName(bbctPushProductInfoVo.getOperateUnitName())
                .projectAreaBusinessNo(bbctPushProductInfoVo.getProjectAreaBusinessNo())
                .projectAreaNo(bbctPushProductInfoVo.getProjectAreaNo())
                .projectAreaName(bbctPushProductInfoVo.getProjectAreaName())
                .projectFormat(bbctPushProductInfoVo.getProjectFormat())
                .projectEstate(bbctPushProductInfoVo.getProjectEstate())
                .projectDistrict("99999")
                .projectLocation("99999")
                //房屋
                .houseId(bbctPushProductInfoVo.getHouseId())
                .houseNo(StringUtils.isNotBlank(bbctPushProductInfoVo.getHouseNo()) ? bbctPushProductInfoVo.getHouseNo() : bbctPushProductInfoVo.getHouseId())
                .houseName(bbctPushProductInfoVo.getHouseName())
                .buildingNo(bbctPushProductInfoVo.getBuildingNo())
                .unitNo(bbctPushProductInfoVo.getUnitNo())
                .floorNo((PaymentEnums.CONTRACTSOURCETYPE_CAR.getCode().equals(vo.getContractSourceType())
                        || PaymentEnums.CONTRACTSOURCETYPE_STORAGE.getCode().equals(vo.getContractSourceType()))
                        ? bbctPushProductInfoVo.getCurrentFloorNo() : bbctPushProductInfoVo.getCurrentFloorNo() + "/" + bbctPushProductInfoVo.getTotalFloorNo())
                .roomNo(bbctPushProductInfoVo.getRoomNo())
                .houseType(bbctPushProductInfoVo.getHouseType())
                .houseOrientation(bbctPushProductInfoVo.getHouseOrientation())
                //合同
                .contractId(vo.getContractId())
                .oldContractId(vo.getOldContractId())
                .contractClassification(vo.getContractClassification())
                .contractType(vo.getContractType())
                .contractStatus(vo.getContractStatus())
                .contractBeginDate(vo.getContractBeginDate())
                .contractEndDate(vo.getContractEndDate())
                .contractSignTime(vo.getContractSignTime())
                .contractCommencementDate(vo.getContractCommencementDate())
                .contractPriceUnit(vo.getContractPriceUnit())
                .contractPricePeriod(vo.getContractPricePeriod())
                .contractArea(vo.getContractArea())
                .roomType(bbctPushProductInfoVo.getRoomType())
                .approver(vo.getApprover())
                .approveTime(vo.getApproveTime())
                .parkPropertyType(bbctPushProductInfoVo.getParkPropertyType())
                .carLocation(bbctPushProductInfoVo.getCarLocation())

                //reits
                .beforeContractId(vo.getBeforeContractId())
                .beforeProjectId(vo.getBeforeProjectId())
                .sceneType(vo.getSceneType())

                .build();

        //家具
        List<BbctFurnitureRentalVo> furnitureRentalList = bbctPushProductInfoVo.getFurnitureRentalList();
        List<FurnitureRentalParamsRequest> furnitureRentalParamsRequestList = new ArrayList<>();
        if (furnitureRentalList != null && furnitureRentalList.size() > 0) {
            for(BbctFurnitureRentalVo bbctFurnitureRentalVo : furnitureRentalList){
                FurnitureRentalParamsRequest furnitureRentalParamsRequest = FurnitureRentalParamsRequest.builder()
                        .furnitureId(bbctFurnitureRentalVo.getFurnitureId())
                        .furnitureName(bbctFurnitureRentalVo.getFurnitureName())
                        .furnitureRental(bbctFurnitureRentalVo.getFurnitureRental())
                        .build();
                furnitureRentalParamsRequestList.add(furnitureRentalParamsRequest);
            }
        } else {
            furnitureRentalParamsRequestList = null;
        }
        storageReletCreateParamsRequest.setFurnitureRentalList(furnitureRentalParamsRequestList);

        bankRequestVo.setData(storageReletCreateParamsRequest);

        ChargeRespondVo result;
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getDefault());
        if (yecaiFeign) {
            log.info("===================================================================================================================");
            try {
                log.info("合同contractId"+vo.getContractId()+",3.54.1仓储续签接口, 请求参数(工银feign)：" + mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            result = bfipChargeFeignClient.storageRelet(bankRequestVo);
        } else {
            String url = yecaiUrl + "/charge/v1/bill/storageRelet";
            try {
                result = restTemplateUtil.postJsonStringByVo(url, mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                throw new McpException("格式转化错误");
            }
        }
        log.info("合同contractId"+vo.getContractId()+",3.54.1仓储续签接口,工银返回:"+JSONObject.toJSONString(result));

        if (!"00000".equals(result.getCode())) {
            throw new McpException("*提示:" + result.getMessage());
        }

        return result.getCode();
    }


    /**
     * 返回一个产品信息
     *
     * @param productInfoList
     * @param index
     * @return
     */
    private BbctPushProductInfoVo getBbctPushProductInfo(List<BbctPushProductInfoVo> productInfoList, int index) {
        BbctPushProductInfoVo bbctPushProductInfoVo = productInfoList.get(index);
        return bbctPushProductInfoVo;
    }

    /**
     * 返回一个签约方信息
     *
     * @param signatoryInfoList
     * @param index
     * @return
     */
    private BbctPushSignatoryInfoVo getBbctPushSignatoryInfo(List<BbctPushSignatoryInfoVo> signatoryInfoList, int index) {
        if(index == signatoryInfoList.size()){
            return null;
        }
        BbctPushSignatoryInfoVo bbctPushSignatoryInfoVo = signatoryInfoList.get(index);
        return bbctPushSignatoryInfoVo;
    }
//    /**
//     * 获取异常详细信息，知道出了什么错，错在哪个类的第几行 .
//     *
//     * @param ex
//     * @return
//     */
//    public static String getExceptionDetail(Exception ex) {
//        String ret = null;
//        try {
//            ByteArrayOutputStream out = new ByteArrayOutputStream();
//            PrintStream pout = new PrintStream(out);
//            ex.printStackTrace(pout);
//            ret = new String(out.toByteArray());
//            pout.close();
//            out.close();
//        } catch (Exception e) {
//        }
//        return ret;
//    }

    private String formatStackTrace(Exception e) {
//        StringBuilder stackTrace = new StringBuilder();
//        for (StackTraceElement element : e.getStackTrace()) {
//            stackTrace.append("\tat ").append(element).append("\n");
//        }
//        return stackTrace.toString();
        return ExceptionUtils.getStackTrace(e);
    }

    private BigDecimal percentageStr(String percentageStr){
        // 去除百分号并将字符串转换为数字
        double percentage = Double.parseDouble(percentageStr.replace("%", ""));
        // 将数字转换为BigDecimal并保留4位小数
        BigDecimal decimalValue = BigDecimal.valueOf(percentage / 100).setScale(4, BigDecimal.ROUND_HALF_UP);
        return decimalValue;
    }

}
