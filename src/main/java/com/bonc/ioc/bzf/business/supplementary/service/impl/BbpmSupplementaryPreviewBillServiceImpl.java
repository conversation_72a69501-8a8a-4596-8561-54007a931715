package com.bonc.ioc.bzf.business.supplementary.service.impl;

import com.bonc.ioc.bzf.business.adjust.utils.RequestUtil;
import com.bonc.ioc.bzf.business.supplementary.entity.BbpmSupplementaryPreviewBillEntity;
import com.bonc.ioc.bzf.business.supplementary.dao.BbpmSupplementaryPreviewBillMapper;
import com.bonc.ioc.bzf.business.supplementary.service.IBbpmSupplementaryPreviewBillService;
import com.bonc.ioc.bzf.utils.common.other.DateUtils;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.session.McpDictSession;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import com.bonc.ioc.common.utils.CurrentUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

import com.bonc.ioc.bzf.business.supplementary.vo.*;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;

import com.bonc.ioc.common.base.page.PageResult;

/**
 * 追加单试算表 服务类实现
 *
 * <AUTHOR>
 * @date 2025-04-08
 * @change 2025-04-08 by pyj for init
 */
@Slf4j
@Service
public class BbpmSupplementaryPreviewBillServiceImpl extends McpBaseServiceImpl<BbpmSupplementaryPreviewBillEntity> implements IBbpmSupplementaryPreviewBillService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbpmSupplementaryPreviewBillMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbpmSupplementaryPreviewBillService baseService;

    /**
     * 字典相关 session实例
     */
    @Resource
    private McpDictSession mcpDictSession;

    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertRecord(BbpmSupplementaryPreviewBillVo vo) {
        if (vo == null) {
            return null;
        }

        BbpmSupplementaryPreviewBillEntity entity = new BbpmSupplementaryPreviewBillEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setBillId(null);
        if (!baseService.insert(entity)) {
            log.error("追加单试算表新增失败:" + entity.toString());
            throw new McpException("追加单试算表新增失败");
        } else {
            if (!baseService.saveOperationHisById(entity.getBillId(), 1)) {
                log.error("追加单试算表新增后保存历史失败:" + entity.toString());
                throw new McpException("追加单试算表新增后保存历史失败");
            }

            log.debug("追加单试算表新增成功:" + entity.getBillId());
            return entity.getBillId();
        }
    }

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbpmSupplementaryPreviewBillVo> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbpmSupplementaryPreviewBillEntity> entityList = new ArrayList<>();
        for (BbpmSupplementaryPreviewBillVo item : voList) {
            BbpmSupplementaryPreviewBillEntity entity = new BbpmSupplementaryPreviewBillEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbpmSupplementaryPreviewBillEntity item : entityList) {
            item.setBillId(null);
        }

        if (!baseService.insertBatch(entityList)) {
            log.error("追加单试算表新增失败");
            throw new McpException("追加单试算表新增失败");
        } else {
            List<String> kidList = entityList.stream().map(BbpmSupplementaryPreviewBillEntity::getBillId).collect(Collectors.toList());

            if (!baseService.saveOperationHisByIds(kidList, 1)) {
                log.error("追加单试算表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("追加单试算表批量新增后保存历史失败");
            }

            log.debug("追加单试算表新增成功:" + StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param billId 需要删除的试算id
     * @return void
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String billId) {
        if (!StringUtils.isEmpty(billId)) {
            if (!baseService.saveOperationHisById(billId, 3)) {
                log.error("追加单试算表删除后保存历史失败:" + billId);
                throw new McpException("追加单试算表删除后保存历史失败");
            }

            if (!baseService.removeById(billId)) {
                log.error("追加单试算表删除失败");
                throw new McpException("追加单试算表删除失败" + billId);
            }
        } else {
            throw new McpException("追加单试算表删除失败试算id为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param billIdList 需要删除的试算id
     * @return void
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> billIdList) {
        if (!CollectionUtils.isEmpty(billIdList)) {
            int oldSize = billIdList.size();
            billIdList = billIdList.stream().filter(t -> StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(billIdList) || oldSize != billIdList.size()) {
                throw new McpException("追加单试算表批量删除失败 存在主键id为空的记录" + StringUtils.join(billIdList));
            }

            if (!baseService.saveOperationHisByIds(billIdList, 3)) {
                log.error("追加单试算表批量删除后保存历史失败:" + StringUtils.join(billIdList));
                throw new McpException("追加单试算表批量删除后保存历史失败");
            }

            if (!baseService.removeByIds(billIdList)) {
                log.error("追加单试算表批量删除失败");
                throw new McpException("追加单试算表批量删除失败" + StringUtils.join(billIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的追加单试算表
     * @return void
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbpmSupplementaryPreviewBillVo vo) {
        if (vo != null) {
            BbpmSupplementaryPreviewBillEntity entity = new BbpmSupplementaryPreviewBillEntity();
            BeanUtils.copyProperties(vo, entity);

            if (StringUtils.isEmpty(entity.getBillId())) {
                throw new McpException("追加单试算表更新失败传入试算id为空");
            }

            if (!baseService.updateById(entity)) {
                log.error("追加单试算表更新失败");
                throw new McpException("追加单试算表更新失败" + entity.getBillId());
            } else {
                if (!baseService.saveOperationHisById(entity.getBillId(), 2)) {
                    log.error("追加单试算表更新后保存历史失败:" + entity.getBillId());
                    throw new McpException("追加单试算表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("追加单试算表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的追加单试算表
     * @return void
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbpmSupplementaryPreviewBillVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbpmSupplementaryPreviewBillEntity> entityList = new ArrayList<>();

            for (BbpmSupplementaryPreviewBillVo item : voList) {
                BbpmSupplementaryPreviewBillEntity entity = new BbpmSupplementaryPreviewBillEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getBillId())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("追加单试算表批量更新失败 存在试算id为空的记录");
            }

            if (!baseService.updateBatchById(entityList)) {
                log.error("追加单试算表批量更新失败");
                throw new McpException("追加单试算表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getBillId())).map(BbpmSupplementaryPreviewBillEntity::getBillId).collect(Collectors.toList());
                if (!baseService.saveOperationHisByIds(kidList, 2)) {
                    log.error("追加单试算表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("追加单试算表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的追加单试算表
     * @return void
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbpmSupplementaryPreviewBillVo vo) {
        if (vo != null) {
            BbpmSupplementaryPreviewBillEntity entity = new BbpmSupplementaryPreviewBillEntity();
            BeanUtils.copyProperties(vo, entity);

            if (!baseService.saveById(entity)) {
                log.error("追加单试算表保存失败");
                throw new McpException("追加单试算表保存失败" + entity.getBillId());
            } else {
                if (!baseService.saveOperationHisById(entity.getBillId(), 4)) {
                    log.error("追加单试算表保存后保存历史失败:" + entity.getBillId());
                    throw new McpException("追加单试算表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("追加单试算表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的追加单试算表
     * @return void
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbpmSupplementaryPreviewBillVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbpmSupplementaryPreviewBillEntity> entityList = new ArrayList<>();

            for (BbpmSupplementaryPreviewBillVo item : voList) {
                BbpmSupplementaryPreviewBillEntity entity = new BbpmSupplementaryPreviewBillEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if (!baseService.saveBatchById(entityList)) {
                log.error("追加单试算表批量保存失败");
                throw new McpException("追加单试算表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getBillId())).map(BbpmSupplementaryPreviewBillEntity::getBillId).collect(Collectors.toList());

                if (!baseService.saveOperationHisByIds(kidList, 4)) {
                    log.error("追加单试算表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("追加单试算表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param billId 需要查询的试算id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbpmSupplementaryPreviewBillVo selectByIdRecord(String billId) {
        BbpmSupplementaryPreviewBillVo vo = new BbpmSupplementaryPreviewBillVo();

        if (!StringUtils.isEmpty(billId)) {
            BbpmSupplementaryPreviewBillEntity entity = baseService.selectById(billId);

            if (entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2025-04-08
     * @change 2025-04-08 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbpmSupplementaryPreviewBillPageResultVo>> selectByPageRecord(BbpmSupplementaryPreviewBillPageVo vo) {
        List<BbpmSupplementaryPreviewBillPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    /**
     * 根据上级id查询
     *
     * @param parentId 上级id
     * @return 查询结果
     */
    @Override
    public List<BbpmSupplementaryPreviewBillVo> selectListByParentId(String parentId) {
        List<BbpmSupplementaryPreviewBillVo> resultList = baseMapper.selectListByParentId(parentId);
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(resultList);
        return resultList;
    }

    /**
     * 获取导出数据
     *
     * @return 导出数据
     */
    @Override
    public List<BbpmSupplementaryExportVo> getExportData() {
        BbpmSupplementaryInfoPageVo vo = new BbpmSupplementaryInfoPageVo();
        vo.setProjectIdStr(RequestUtil.getProjects());
        List<BbpmSupplementaryExportVo> resultList = baseMapper.selectExportData(vo);
        for (BbpmSupplementaryExportVo exportVo : resultList) {
            String contractTimeStr = String.format("%s~%s",
                    DateUtils.formatDate(exportVo.getContractBeginTime(), "yyyy/MM/dd"),
                    DateUtils.formatDate(exportVo.getContractEndTime(), "yyyy/MM/dd"));
            exportVo.setContractTimeStr(contractTimeStr);
            String chargeSubjectPeriodStr = String.format("第%s期（%s 至 %s）",
                    exportVo.getChargeSubjectPeriod(),
                    DateUtils.formatDate(exportVo.getChargeSubjectBeginDate(), "yyyy/MM/dd"),
                    DateUtils.formatDate(exportVo.getChargeSubjectEndDate(), "yyyy/MM/dd"));
            exportVo.setChargeSubjectPeriodStr(chargeSubjectPeriodStr);
        }
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(resultList);
        return resultList;
    }
}
