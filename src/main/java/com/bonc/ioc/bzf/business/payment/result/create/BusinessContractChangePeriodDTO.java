package com.bonc.ioc.bzf.business.payment.result.create;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 缴费周期变更 (contractChangePeriodDTO)
 */
@Data
public class BusinessContractChangePeriodDTO implements Serializable {

    @ApiModelProperty(value = "变更合同号")
    private String contractCode;

    @ApiModelProperty(value = "协议号")
    private String agreementCode;

    @ApiModelProperty(value = "变更账期开始时间")
    private String changeBillStartDate; // "yyyy-MM-dd"

    @ApiModelProperty(value = "变更账期结束时间")
    private String changeBillEndDate; // "yyyy-MM-dd"

    @ApiModelProperty(value = "变更后的缴费周期")
    private String chargeSubjectPeriod; // 01月 02季 03半年 04年 05两个月 06四个月

    @ApiModelProperty(value = "商铺信息")
    private List<Room> roomList; // 按照最后生成账单规则传，只改缴费周期部分
} 