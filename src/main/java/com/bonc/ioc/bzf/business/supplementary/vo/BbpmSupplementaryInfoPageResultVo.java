package com.bonc.ioc.bzf.business.supplementary.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

import com.bonc.ioc.common.validator.inf.*;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 追加单表 实体类
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
@Data
@ApiModel(value = "BbpmSupplementaryInfoPageResultVo对象", description = "追加单表")
public class BbpmSupplementaryInfoPageResultVo extends McpBasePageVo implements Serializable {

    /**
     * 追加单id
     */
    @ApiModelProperty(value = "追加单id")
    @NotBlank(message = "追加单id不能为空", groups = {UpdateValidatorGroup.class})
    private String supplementaryId;

    /**
     * 追加单号
     */
    @ApiModelProperty(value = "追加单号")
    private String supplementaryCode;

    /**
     * 追加单状态(1.暂存 2.未通过 3.待审核 4.已完成)
     */
    @ApiModelProperty(value = "追加单状态(1.暂存 2.未通过 3.待审核 4.已完成)")
    @McpDictPoint(dictCode = "SUPPLEMENTARY_STATUS", overTransCopyTo = "supplementaryStatusName")
    private String supplementaryStatus;

    /**
     * 追加单状态名称
     */
    @ApiModelProperty(value = "追加单状态名称")
    private String supplementaryStatusName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    /**
     * 签约类型(01.散租 02.趸租 03.管理协议)
     */
    @ApiModelProperty(value = "签约类型(01.散租 02.趸租 03.管理协议)")
    @McpDictPoint(dictCode = "SIGN_TYPE", overTransCopyTo = "signTypeName")
    private String signType;

    /**
     * 签约类型名称
     */
    @ApiModelProperty(value = "签约类型名称")
    private String signTypeName;

    /**
     * 产品类型(01.公租房 07.保租房)
     */
    @ApiModelProperty(value = "产品类型(01.公租房 07.保租房)")
        @McpDictPoint(dictCode = "PRODUCT_TYPE", overTransCopyTo = "productTypeName")
    private String productType;

    /**
     * 产品类型名称
     */
    @ApiModelProperty(value = "产品类型名称")
    private String productTypeName;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String customerIdNumber;

    /**
     * 社会统一信息用代码
     */
    @ApiModelProperty(value = "社会统一信息用代码")
    private String customerCreditCode;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 追加账单依据
     */
    @ApiModelProperty(value = "追加账单依据")
    private String supplementaryFile;

    /**
     * 合同开始时间
     */
    @ApiModelProperty(value = "合同开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractBeginTime;

    /**
     * 合同结束时间
     */
    @ApiModelProperty(value = "合同结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndTime;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
    private String delFlag;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    /**
     * 提交人名称
     */
    @ApiModelProperty(value = "提交人名称")
    private String submitUserName;

    /**
     * 审批人名称
     */
    @ApiModelProperty(value = "审批人名称")
    private String approverUserName;

    /**
     * 审批状态(1.通过 2.未通过 3.待审核 4.撤回)
     */
    @ApiModelProperty(value = "审批状态(1.通过 2.未通过 3.待审核 4.撤回)")
    @McpDictPoint(dictCode = "SUPPLEMENTARY_APPROVE_STATUS", overTransCopyTo = "approveStatusName")
    private String approveStatus;

    /**
     * 追加单状态名称
     */
    @ApiModelProperty(value = "追加单状态名称")
    private String approveStatusName;
}
