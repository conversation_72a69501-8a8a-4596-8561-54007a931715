package com.bonc.ioc.bzf.business.supplementary.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

import com.bonc.ioc.common.validator.inf.*;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 追加单表 实体类
 *
 * <AUTHOR>
 * @date 2025-03-26
 * @change 2025-03-26 by pyj for init
 */
@Data
@ApiModel(value = "BbpmSupplementaryInfoPageVo对象", description = "追加单表")
public class BbpmSupplementaryInfoPageVo extends McpBasePageVo implements Serializable {

    /**
     * 追加单id
     */
    @ApiModelProperty(value = "追加单id")
    @NotBlank(message = "追加单id不能为空", groups = {UpdateValidatorGroup.class})
    private String supplementaryId;

    /**
     * 追加单号
     */
    @ApiModelProperty(value = "追加单号")
    private String supplementaryCode;

    /**
     * 追加单状态(1.暂存 2.未通过 3.待审核 4.已完成)
     */
    @ApiModelProperty(value = "追加单状态(1.暂存 2.未通过 3.待审核 4.已完成)")
    private String supplementaryStatus;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    /**
     * 签约类型(01.散租 02.趸租 03.管理协议)
     */
    @ApiModelProperty(value = "签约类型(01.散租 02.趸租 03.管理协议)")
    private String signType;

    /**
     * 产品类型(01.公租房 07.保租房)
     */
    @ApiModelProperty(value = "产品类型(01.公租房 07.保租房)")
    private String productType;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String customerIdNumber;

    /**
     * 社会统一信息用代码
     */
    @ApiModelProperty(value = "社会统一信息用代码")
    private String customerCreditCode;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 追加账单依据
     */
    @ApiModelProperty(value = "追加账单依据")
    private String supplementaryFile;

    /**
     * 合同开始时间
     */
    @ApiModelProperty(value = "合同开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractBeginTime;

    /**
     * 合同结束时间
     */
    @ApiModelProperty(value = "合同结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndTime;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
    private String delFlag;

    /**
     * 创建开始时间
     */
    @ApiModelProperty(value = "创建开始时间")
    private String createTimeBegin;

    /**
     * 创建结束时间
     */
    @ApiModelProperty(value = "创建结束时间")
    private String createTimeEnd;

    /**
     * 项目编号ids 逗号分隔
     */
    @ApiModelProperty(value = "项目编号ids 逗号分隔")
    private String projectIdStr;

    /**
     * 追加单状态 逗号分隔
     */
    @ApiModelProperty(value = "追加单状态 逗号分隔")
    private String supplementaryStatusStr;

    /**
     * 审批状态
     */
    @ApiModelProperty(value = "审批状态")
    private String approveStatus;

    /**
     * 提交开始时间
     */
    @ApiModelProperty(value = "提交开始时间")
    private String submitTimeBegin;

    /**
     * 提交结束时间
     */
    @ApiModelProperty(value = "提交结束时间")
    private String submitTimeEnd;

    /**
     * 是否需要存在审批信息(0.否 1.是)
     */
    @ApiModelProperty(value = "是否需要存在审批信息(0.否 1.是)")
    private String existApprove;
}
