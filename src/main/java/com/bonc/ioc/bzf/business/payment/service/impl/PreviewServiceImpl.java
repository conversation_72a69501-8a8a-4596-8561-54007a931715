package com.bonc.ioc.bzf.business.payment.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.bonc.ioc.bzf.business.payment.feign.feign.BfipChargeFeignClient;
import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.service.PreviewService;
import com.bonc.ioc.bzf.business.payment.utils.RestTemplateUtil;
import com.bonc.ioc.bzf.business.payment.vo.*;
import com.bonc.ioc.common.exception.McpException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.TimeZone;

@Slf4j
@Service
public class PreviewServiceImpl implements PreviewService {
    @Resource
    private BfipChargeFeignClient bfipChargeFeignClient;

    @Resource
    private RestTemplateUtil restTemplateUtil ;

    @Value("${yecai.feign}")
    private boolean yecaiFeign;

    @Value("${yecai.url}")
    private String yecaiUrl;

    /**
     * 3.39. 商业合同账单预览接口
     *
     * @param previewBillsParamsVo
     * @return
     */
    @Override
    public PreviewBillsResultVo getPreviewBills(PreviewBillsParamsVo previewBillsParamsVo) {
        ParentRequest<PreviewBillsParamsVo> bankRequestVo = new ParentRequest<>();
        bankRequestVo.setData(previewBillsParamsVo);
        String resultString = null;
        ChargeRespondVo<PreviewBillsResultVo> result;
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getDefault());
        if (yecaiFeign) {
            log.info("===================================================================================================================");
            try {
                log.info(previewBillsParamsVo.getContractId()+"3.39. 商业合同账单预览接口, 请求参数(工银feign)：" + mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
           // resultString = bfipChargeFeignClient.getPreviewBillsString(bankRequestVo);
            //ChargeRespondVo<?> x =JSONObject.parseObject(resultString,new TypeReference<ChargeRespondVo>(){});
            result =  bfipChargeFeignClient.getPreviewBills(bankRequestVo);
        } else {
            String url = yecaiUrl + "/charge/v1/bill/business/getPreviewBills";
            try {
                result = restTemplateUtil.postJsonStringByVo(url, mapper.writeValueAsString(bankRequestVo));
            } catch (JsonProcessingException e) {
                throw new McpException("格式转化错误");
            }
        }
        log.info(previewBillsParamsVo.getContractId()+"3.39. 商业合同账单预览接口,工银返回:"+ JSONObject.toJSONString(result));

        if (!"00000".equals(result.getCode())) {
            throw new McpException("*提示:" + result.getMessage());
        }
        return result.getData();
    }

    public static void main(String[] args) {
        String s="{\n" +
                "    \"busiCode\": \"\",\n" +
                "    \"code\": \"00000\",\n" +
                "    \"data\": {\n" +
                "        \"depositList\": [\n" +
                "            {\n" +
                "                \"chargePeriod\": \"1\"\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"message\": \"收款开户行查询成功\"\n" +
                "}";
        ChargeRespondVo<PreviewBillsResultVo> t= JSONObject.parseObject(s,ChargeRespondVo.class);
        System.out.println(t.getData().getDepositList().toString());
    }
}
