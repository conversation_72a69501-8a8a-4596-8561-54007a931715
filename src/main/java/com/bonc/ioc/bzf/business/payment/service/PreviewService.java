package com.bonc.ioc.bzf.business.payment.service;

import com.bonc.ioc.bzf.business.payment.result.ParentRequest;
import com.bonc.ioc.bzf.business.payment.vo.ChangeTrialPreviewBillsResultVo;
import com.bonc.ioc.bzf.business.payment.vo.ChargeRespondVo;
import com.bonc.ioc.bzf.business.payment.vo.PreviewBillsParamsVo;
import com.bonc.ioc.bzf.business.payment.vo.PreviewBillsResultVo;
import com.bonc.ioc.common.util.AppReply;

public interface PreviewService {

    /**
     * 3.39. 商业合同账单预览接口
     * @param previewBillsParamsVo
     * @return
     */
    PreviewBillsResultVo getPreviewBills(PreviewBillsParamsVo previewBillsParamsVo);



}
