<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.supplementary.dao.BbpmSupplementaryPreviewBillMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.bonc.ioc.bzf.business.supplementary.entity.BbpmSupplementaryPreviewBillEntity">
        <id column="bill_id" property="billId" javaType="String"/>
        <result column="create_user" property="createUser" javaType="String"/>
        <result column="create_time" property="createTime" javaType="Date"/>
        <result column="modify_user" property="modifyUser" javaType="String"/>
        <result column="modify_time" property="modifyTime" javaType="Date"/>
        <result column="cid" property="cid" javaType="Long"/>
        <result column="tenant_id" property="tenantId" javaType="Long"/>
        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
        <result column="parent_id" property="parentId" javaType="String"/>
        <result column="charge_subject" property="chargeSubject" javaType="String"/>
        <result column="tenant_name" property="tenantName" javaType="String"/>
        <result column="org_name" property="orgName" javaType="String"/>
        <result column="room_address" property="roomAddress" javaType="String"/>
        <result column="payable_date" property="payableDate" javaType="Date"/>
        <result column="payable_money" property="payableMoney" javaType="String"/>
        <result column="no_tax_money" property="noTaxMoney" javaType="String"/>
        <result column="tax_rate" property="taxRate" javaType="String"/>
        <result column="rate_money" property="rateMoney" javaType="String"/>
        <result column="bill_owner" property="billOwner" javaType="String"/>
        <result column="cyclic_or_single" property="cyclicOrSingle" javaType="String"/>
        <result column="charge_subject_begin_date" property="chargeSubjectBeginDate" javaType="Date"/>
        <result column="charge_subject_end_date" property="chargeSubjectEndDate" javaType="Date"/>
        <result column="charge_subject_period" property="chargeSubjectPeriod" javaType="Integer"/>
        <result column="del_flag" property="delFlag" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap"
               type="com.bonc.ioc.bzf.business.supplementary.vo.BbpmSupplementaryPreviewBillPageResultVo">
        <result column="parent_id" property="parentId" javaType="String"/>
        <result column="charge_subject" property="chargeSubject" javaType="String"/>
        <result column="tenant_name" property="tenantName" javaType="String"/>
        <result column="org_name" property="orgName" javaType="String"/>
        <result column="room_address" property="roomAddress" javaType="String"/>
        <result column="payable_date" property="payableDate" javaType="Date"/>
        <result column="payable_money" property="payableMoney" javaType="String"/>
        <result column="no_tax_money" property="noTaxMoney" javaType="String"/>
        <result column="tax_rate" property="taxRate" javaType="String"/>
        <result column="rate_money" property="rateMoney" javaType="String"/>
        <result column="bill_owner" property="billOwner" javaType="String"/>
        <result column="cyclic_or_single" property="cyclicOrSingle" javaType="String"/>
        <result column="charge_subject_begin_date" property="chargeSubjectBeginDate" javaType="Date"/>
        <result column="charge_subject_end_date" property="chargeSubjectEndDate" javaType="Date"/>
        <result column="charge_subject_period" property="chargeSubjectPeriod" javaType="Integer"/>
        <result column="del_flag" property="delFlag" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.cid
        ,base.tenant_id
        ,base.bill_id
        ,base.parent_id
        ,base.charge_subject
        ,base.tenant_name
        ,base.org_name
        ,base.room_address
        ,base.payable_date
        ,base.payable_money
        ,base.no_tax_money
        ,base.tax_rate
        ,base.rate_money
        ,base.bill_owner
        ,base.cyclic_or_single
        ,base.charge_subject_begin_date
        ,base.charge_subject_end_date
        ,base.charge_subject_period
        ,base.del_flag
    </sql>

    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_supplementary_preview_bill base
        <where>
            <if test="'' != vo.billId and vo.billId != null">
                and base.bill_id = #{vo.billId}
            </if>
            <if test="'' != vo.parentId and vo.parentId != null">
                and base.parent_id = #{vo.parentId}
            </if>
            <if test="'' != vo.chargeSubject and vo.chargeSubject != null">
                and base.charge_subject = #{vo.chargeSubject}
            </if>
            <if test="'' != vo.tenantName and vo.tenantName != null">
                and base.tenant_name = #{vo.tenantName}
            </if>
            <if test="'' != vo.orgName and vo.orgName != null">
                and base.org_name = #{vo.orgName}
            </if>
            <if test="'' != vo.roomAddress and vo.roomAddress != null">
                and base.room_address = #{vo.roomAddress}
            </if>
            <if test="vo.payableDate != null">
                and base.payable_date = #{vo.payableDate}
            </if>
            <if test="'' != vo.payableMoney and vo.payableMoney != null">
                and base.payable_money = #{vo.payableMoney}
            </if>
            <if test="'' != vo.noTaxMoney and vo.noTaxMoney != null">
                and base.no_tax_money = #{vo.noTaxMoney}
            </if>
            <if test="'' != vo.taxRate and vo.taxRate != null">
                and base.tax_rate = #{vo.taxRate}
            </if>
            <if test="'' != vo.rateMoney and vo.rateMoney != null">
                and base.rate_money = #{vo.rateMoney}
            </if>
            <if test="'' != vo.billOwner and vo.billOwner != null">
                and base.bill_owner = #{vo.billOwner}
            </if>
            <if test="'' != vo.cyclicOrSingle and vo.cyclicOrSingle != null">
                and base.cyclic_or_single = #{vo.cyclicOrSingle}
            </if>
            <if test="vo.chargeSubjectBeginDate != null">
                and base.charge_subject_begin_date = #{vo.chargeSubjectBeginDate}
            </if>
            <if test="vo.chargeSubjectEndDate != null">
                and base.charge_subject_end_date = #{vo.chargeSubjectEndDate}
            </if>
            <if test="'' != vo.chargeSubjectPeriod and vo.chargeSubjectPeriod != null">
                and base.charge_subject_period = #{vo.chargeSubjectPeriod}
            </if>
            <if test="'' != vo.delFlag and vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>
    <select id="selectListByParentId"
            resultType="com.bonc.ioc.bzf.business.supplementary.vo.BbpmSupplementaryPreviewBillVo">
        SELECT
        base.*
        FROM bbpm_supplementary_preview_bill base
        WHERE
        base.del_flag = '1'
        AND
        base.parent_id = #{parentId}
    </select>
    <select id="selectExportData"
            resultType="com.bonc.ioc.bzf.business.supplementary.vo.BbpmSupplementaryExportVo">
        SELECT
        si.supplementary_code,
        si.customer_name,
        si.customer_id_number,
        si.company_name,
        si.customer_credit_code,
        si.contract_no,
        si.contract_begin_time,
        si.contract_end_time,
        si.sign_type,
        pb.charge_subject_period,
        pb.charge_subject_begin_date,
        pb.charge_subject_end_date,
        pb.charge_subject,
        pb.bill_owner,
        pb.room_address,
        pb.payable_date,
        pb.payable_money,
        pb.tax_rate,
        pb.no_tax_money,
        pb.rate_money,
        pb.cyclic_or_single
        FROM
        bbpm_supplementary_preview_bill pb
        LEFT JOIN bbpm_supplementary_info si ON si.supplementary_id = pb.parent_id
        <where>
            si.del_flag = '1'
            and si.supplementary_status = 4
            <if test="'' != vo.projectIdStr and vo.projectIdStr != null">
                and si.project_id IN
                <foreach item="item" index="index" collection="vo.projectIdStr.split(',')" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY
        si.create_time DESC,
        pb.charge_subject_period
    </select>
</mapper>
