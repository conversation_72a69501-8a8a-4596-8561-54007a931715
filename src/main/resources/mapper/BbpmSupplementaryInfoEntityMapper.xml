<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.supplementary.dao.BbpmSupplementaryInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.supplementary.entity.BbpmSupplementaryInfoEntity">
        <id column="supplementary_id" property="supplementaryId" javaType="String"/>
        <result column="create_user" property="createUser" javaType="String"/>
        <result column="create_time" property="createTime" javaType="Date"/>
        <result column="modify_user" property="modifyUser" javaType="String"/>
        <result column="modify_time" property="modifyTime" javaType="Date"/>
        <result column="cid" property="cid" javaType="Long"/>
        <result column="tenant_id" property="tenantId" javaType="Long"/>
        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
        <result column="supplementary_code" property="supplementaryCode" javaType="String"/>
        <result column="supplementary_status" property="supplementaryStatus" javaType="String"/>
        <result column="contract_no" property="contractNo" javaType="String"/>
        <result column="sign_type" property="signType" javaType="String"/>
        <result column="product_type" property="signType" javaType="String"/>
        <result column="project_id" property="projectId" javaType="String"/>
        <result column="product_name" property="productName" javaType="String"/>
        <result column="customer_name" property="customerName" javaType="String"/>
        <result column="company_name" property="companyName" javaType="String"/>
        <result column="customer_id_number" property="customerIdNumber" javaType="String"/>
        <result column="customer_credit_code" property="customerCreditCode" javaType="String"/>
        <result column="create_user_name" property="createUserName" javaType="String"/>
        <result column="supplementary_file" property="supplementaryFile" javaType="String"/>
        <result column="contract_begin_time" property="contractBeginTime" javaType="Date"/>
        <result column="contract_end_time" property="contractEndTime" javaType="Date"/>
        <result column="del_flag" property="delFlag" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.supplementary.vo.BbpmSupplementaryInfoPageResultVo">
        <result column="supplementary_code" property="supplementaryCode" javaType="String"/>
        <result column="supplementary_status" property="supplementaryStatus" javaType="String"/>
        <result column="contract_no" property="contractNo" javaType="String"/>
        <result column="sign_type" property="signType" javaType="String"/>
        <result column="product_type" property="productType" javaType="String"/>
        <result column="project_id" property="projectId" javaType="String"/>
        <result column="product_name" property="productName" javaType="String"/>
        <result column="customer_name" property="customerName" javaType="String"/>
        <result column="company_name" property="companyName" javaType="String"/>
        <result column="customer_id_number" property="customerIdNumber" javaType="String"/>
        <result column="customer_credit_code" property="customerCreditCode" javaType="String"/>
        <result column="create_user_name" property="createUserName" javaType="String"/>
        <result column="supplementary_file" property="supplementaryFile" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="String"/>
        <result column="create_time" property="createTime" javaType="Date"/>
        <result column="submit_time" property="submitTime" javaType="Date"/>
        <result column="approve_time" property="approveTime" javaType="Date"/>
        <result column="submit_user_name" property="submitUserName" javaType="String"/>
        <result column="approver_user_name" property="approverUserName" javaType="String"/>
        <result column="contract_begin_time" property="contractBeginTime" javaType="Date"/>
        <result column="contract_end_time" property="contractEndTime" javaType="Date"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.cid
        ,base.tenant_id
        ,base.supplementary_id
        ,base.supplementary_code
        ,base.supplementary_status
        ,base.contract_no
        ,base.sign_type
        ,base.product_type
        ,base.project_id
        ,base.product_name
        ,base.customer_name
        ,base.company_name
        ,base.customer_id_number
        ,base.customer_credit_code
        ,base.create_user_name
        ,base.supplementary_file
        ,base.contract_begin_time
        ,base.contract_end_time
        ,base.del_flag
    </sql>

    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_supplementary_info base
        <where>
            <if test="'' != vo.supplementaryId and vo.supplementaryId != null">
                and base.supplementary_id = #{vo.supplementaryId}
            </if>
            <if test="'' != vo.supplementaryCode and vo.supplementaryCode != null">
                and base.supplementary_code = #{vo.supplementaryCode}
            </if>
            <if test="'' != vo.supplementaryStatus and vo.supplementaryStatus != null">
                and base.supplementary_status = #{vo.supplementaryStatus}
            </if>
            <if test="'' != vo.contractNo and vo.contractNo != null">
                and base.contract_no = #{vo.contractNo}
            </if>
            <if test="'' != vo.signType and vo.signType != null">
                and base.sign_type = #{vo.signType}
            </if>
            <if test="'' != vo.productType and vo.productType != null">
                and base.product_type = #{vo.productType}
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
            <if test="'' != vo.productName and vo.productName != null">
                and base.product_name = #{vo.productName}
            </if>
            <if test="'' != vo.customerName and vo.customerName != null">
                and base.customer_name = #{vo.customerName}
            </if>
            <if test="'' != vo.companyName and vo.companyName != null">
                and base.company_name = #{vo.companyName}
            </if>
            <if test="'' != vo.createUserName and vo.createUserName != null">
                and base.create_user_name = #{vo.createUserName}
            </if>
            <if test="'' != vo.supplementaryFile and vo.supplementaryFile != null">
                and base.supplementary_file = #{vo.supplementaryFile}
            </if>
            <if test="'' != vo.delFlag and vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>
    <select id="selectSupplementaryInfoPage" resultMap="QueryResultMap">
        select
        ap.submit_time,
        ap.approve_time,
        ap.approve_status,
        ap.submit_user_name,
        ap.approver_user_name,
        <include refid="Base_Column_List"/>
        from bbpm_supplementary_info base
        left join bbpm_approve_info ap on ap.parent_id = base.supplementary_id
        <where>
            base.del_flag = '1'
            <if test="'' != vo.projectIdStr and vo.projectIdStr != null">
                and base.project_id IN
                <foreach item="item" index="index" collection="vo.projectIdStr.split(',')" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
            <if test="'' != vo.supplementaryStatusStr and vo.supplementaryStatusStr != null">
                and base.supplementary_status IN
                <foreach item="item" index="index" collection="vo.supplementaryStatusStr.split(',')" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="'' != vo.supplementaryStatus and vo.supplementaryStatus != null">
                and base.supplementary_status = #{vo.supplementaryStatus}
            </if>
            <if test="'' != vo.signType and vo.signType != null">
                and base.sign_type = #{vo.signType}
            </if>
            <if test="'' != vo.supplementaryCode and vo.supplementaryCode != null">
                and base.supplementary_code like concat('%',#{vo.supplementaryCode},'%')
            </if>
            <if test="'' != vo.contractNo and vo.contractNo != null">
                and base.contract_no like concat('%',#{vo.contractNo},'%')
            </if>
            <if test="'' != vo.productName and vo.productName != null">
                and base.product_name like concat('%',#{vo.productName},'%')
            </if>
            <if test="'' != vo.customerName and vo.customerName != null">
                and base.customer_name like concat('%',#{vo.customerName},'%')
            </if>
            <if test="'' != vo.companyName and vo.companyName != null">
                and base.company_name like concat('%',#{vo.companyName},'%')
            </if>
            <if test="'' != vo.createUserName and vo.createUserName != null">
                and base.create_user_name like concat('%',#{vo.createUserName},'%')
            </if>
            <if test="'' != vo.createTimeEnd and vo.createTimeEnd != null">
                and base.create_time &lt;= concat(#{vo.createTimeEnd},' 23:59:59')
            </if>
            <if test="'' != vo.createTimeBegin and vo.createTimeBegin != null">
                and base.create_time &gt;= concat(#{vo.createTimeBegin},' 00:00:00')
            </if>
            <if test="'' != vo.submitTimeEnd and vo.submitTimeEnd != null">
                and ap.submit_time &lt;= concat(#{vo.submitTimeEnd},' 23:59:59')
            </if>
            <if test="'' != vo.submitTimeBegin and vo.submitTimeBegin != null">
                and ap.submit_time &gt;= concat(#{vo.submitTimeBegin},' 00:00:00')
            </if>
            <if test="'' != vo.approveStatus and vo.approveStatus != null">
                and ap.approve_status = #{vo.approveStatus}
            </if>
            <if test="'' != vo.existApprove and vo.existApprove != null">
                <if test="'1'.toString() == vo.existApprove">
                    and ap.approve_id is not null
                </if>
            </if>
        </where>
        <if test="'' != vo.existApprove and vo.existApprove != null">
            <if test="'1'.toString() == vo.existApprove">
                ORDER BY ap.modify_time desc
            </if>
            <if test="'0'.toString() == vo.existApprove">
                ORDER BY base.create_time desc
            </if>
        </if>
    </select>
    <select id="statistic" resultType="java.lang.Integer">
        SELECT
        count( 1 )
        FROM
        bbpm_supplementary_info base
        <if test="'' != vo.existApprove and vo.existApprove != null">
            left join bbpm_approve_info ap on ap.parent_id = base.supplementary_id
        </if>
        <where>
            base.del_flag = '1'
            <if test="'' != vo.projectIdStr and vo.projectIdStr != null">
                and base.project_id IN
                <foreach item="item" index="index" collection="vo.projectIdStr.split(',')" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="'' != vo.supplementaryStatusStr and vo.supplementaryStatusStr != null">
                and base.supplementary_status IN
                <foreach item="item" index="index" collection="vo.supplementaryStatusStr.split(',')" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="'' != vo.existApprove and vo.existApprove != null">
                <if test="'1'.toString() == vo.existApprove">
                    and ap.approve_id is not null
                </if>
            </if>
        </where>
    </select>
    <select id="selectDoingSupplementaryInfoSize" resultType="java.lang.Integer">
        SELECT
        count( 1 )
        FROM
        bbpm_supplementary_info base
        WHERE
        base.supplementary_status != 4
        AND
        base.contract_no = #{contractNo}
    </select>
    <select id="selectSupplementaryCodeSerial" resultType="string">
        SELECT
        SUBSTRING( supplementary_code, - 4 ) AS serialStr
        FROM
        bbpm_supplementary_info
        WHERE supplementary_code like concat('%', #{formattedDate}, '%')
        ORDER BY
        create_time DESC
        LIMIT 1
    </select>
</mapper>
