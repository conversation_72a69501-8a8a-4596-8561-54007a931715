<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.adjust.dao.BbpmReceivableAdjustMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.adjust.entity.BbpmReceivableAdjustEntity">
                            <id column="id" property="id" javaType="String"/>
                        <result column="create_user" property="createUser" javaType="String"/>
                        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="modify_user" property="modifyUser" javaType="String"/>
                        <result column="modify_time" property="modifyTime" javaType="Date"/>
                        <result column="tenant_id" property="tenantId" javaType="Long"/>
                        <result column="cid" property="cid" javaType="Long"/>
                        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
                            <result column="adjust_no" property="adjustNo" javaType="String"/>
                            <result column="product_address" property="productAddress" javaType="String"/>
                            <result column="customer_name" property="customerName" javaType="String"/>
                            <result column="customer_id_type" property="customerIdType" javaType="String"/>
                            <result column="customer_id_number" property="customerIdNumber" javaType="String"/>
                            <result column="company" property="company" javaType="String"/>
                            <result column="customer_credit_code" property="customerCreditCode" javaType="String"/>
                            <result column="contract_no" property="contractNo" javaType="String"/>
                            <result column="contract_type" property="contractType" javaType="String"/>
                            <result column="contract_begin_time" property="contractBeginTime" javaType="Date"/>
                            <result column="contract_end_time" property="contractEndTime" javaType="Date"/>
                            <result column="project_id" property="projectId" javaType="String"/>
                            <result column="project_name" property="projectName" javaType="String"/>
                            <result column="adjust_status" property="adjustStatus" javaType="String"/>
                            <result column="refund_status" property="refundStatus" javaType="String"/>
                            <result column="failure_reason" property="failureReason" javaType="String"/>
        <result column="qy_failure_reason" property="qyFailureReason" javaType="String"/>
        <result column="calculation_results" property="calculationResults" javaType="String"/>
                            <result column="adjust_amount" property="adjustAmount" javaType="BigDecimal"/>
                            <result column="adjust_files" property="adjustFiles" javaType="String"/>
                            <result column="amount_handle" property="amountHandle" javaType="String"/>
                            <result column="refund_type" property="refundType" javaType="String"/>
                            <result column="refund_path" property="refundPath" javaType="String"/>
                            <result column="account_holder" property="accountHolder" javaType="String"/>
                            <result column="bank_card" property="bankCard" javaType="String"/>
                            <result column="opening_bank" property="openingBank" javaType="String"/>
        <result column="bank_branch_code" property="bankBranchCode" javaType="String"/>
                            <result column="opening_province" property="openingProvince" javaType="String"/>
                            <result column="opening_city" property="openingCity" javaType="String"/>
        <result column="change_bank_reason" property="changeBankReason" javaType="String"/>
        <result column="bank_files" property="bankFiles" javaType="String"/>
        <result column="bank_id_number" property="bankIdNumber" javaType="String"/>
        <result column="qy_refund_path" property="qyRefundPath" javaType="String"/>
        <result column="qy_account_holder" property="qyAccountHolder" javaType="String"/>
        <result column="qy_bank_card" property="qyBankCard" javaType="String"/>
        <result column="qy_bank_branch_code" property="qyBankBranchCode" javaType="String"/>
        <result column="qy_opening_bank" property="qyOpeningBank" javaType="String"/>
        <result column="qy_opening_province" property="qyOpeningProvince" javaType="String"/>
        <result column="qy_opening_city" property="qyOpeningCity" javaType="String"/>
        <result column="qy_change_bank_reason" property="qyChangeBankReason" javaType="String"/>

        <result column="opening_bank_name" property="openingBankName" javaType="String"/>
        <result column="bank_branch_name" property="bankBranchName" javaType="String"/>
        <result column="opening_province_name" property="openingProvinceName" javaType="String"/>
        <result column="opening_city_name" property="openingCityName" javaType="String"/>
        <result column="qy_opening_bank_name" property="qyOpeningBankName" javaType="String"/>
        <result column="qy_bank_branch_name" property="qyBankBranchName" javaType="String"/>
        <result column="qy_opening_province_name" property="qyOpeningProvinceName" javaType="String"/>
        <result column="qy_opening_city_name" property="qyOpeningCityName" javaType="String"/>

        <result column="qy_bank_files" property="qyBankFiles" javaType="String"/>
        <result column="qy_bank_id_number" property="qyBankIdNumber" javaType="String"/>
                            <result column="ext1" property="ext1" javaType="String"/>
                            <result column="ext2" property="ext2" javaType="String"/>
                            <result column="ext3" property="ext3" javaType="String"/>
                            <result column="del_flag" property="delFlag" javaType="Integer"/>
                            <result column="create_user_name" property="createUserName" javaType="String"/>
    </resultMap>

    <!--    查询详情-->
    <resultMap id="BaseResultMapForId" type="com.bonc.ioc.bzf.business.adjust.vo.BbpmReceivableAdjustVo">
        <id column="id" property="id" javaType="String"/>
        <result column="adjust_no" property="adjustNo" javaType="String"/>
        <result column="product_address" property="productAddress" javaType="String"/>
        <result column="customer_name" property="customerName" javaType="String"/>
        <result column="customer_id_type" property="customerIdType" javaType="String"/>
        <result column="customer_id_number" property="customerIdNumber" javaType="String"/>
        <result column="company" property="company" javaType="String"/>
        <result column="customer_credit_code" property="customerCreditCode" javaType="String"/>
        <result column="contract_no" property="contractNo" javaType="String"/>
        <result column="contract_type" property="contractType" javaType="String"/>
        <result column="contract_begin_time" property="contractBeginTime" javaType="Date"/>
        <result column="contract_end_time" property="contractEndTime" javaType="Date"/>
        <result column="project_id" property="projectId" javaType="String"/>
        <result column="project_name" property="projectName" javaType="String"/>
        <result column="adjust_status" property="adjustStatus" javaType="String"/>
        <result column="refund_status" property="refundStatus" javaType="String"/>
        <result column="failure_reason" property="failureReason" javaType="String"/>
        <result column="qy_failure_reason" property="qyFailureReason" javaType="String"/>
        <result column="calculation_results" property="calculationResults" javaType="String"/>
        <result column="adjust_amount" property="adjustAmount" javaType="BigDecimal"/>
        <result column="adjust_files" property="adjustFiles" javaType="String"/>
        <result column="amount_handle" property="amountHandle" javaType="String"/>
        <result column="refund_type" property="refundType" javaType="String"/>
        <result column="refund_path" property="refundPath" javaType="String"/>
        <result column="account_holder" property="accountHolder" javaType="String"/>
        <result column="bank_card" property="bankCard" javaType="String"/>
        <result column="opening_bank" property="openingBank" javaType="String"/>
        <result column="bank_branch_code" property="bankBranchCode" javaType="String"/>
        <result column="opening_province" property="openingProvince" javaType="String"/>
        <result column="opening_city" property="openingCity" javaType="String"/>
        <result column="change_bank_reason" property="changeBankReason" javaType="String"/>
        <result column="bank_files" property="bankFiles" javaType="String"/>
        <result column="bank_id_number" property="bankIdNumber" javaType="String"/>
        <result column="qy_refund_path" property="qyRefundPath" javaType="String"/>
        <result column="qy_account_holder" property="qyAccountHolder" javaType="String"/>
        <result column="qy_bank_card" property="qyBankCard" javaType="String"/>
        <result column="qy_bank_branch_code" property="qyBankBranchCode" javaType="String"/>
        <result column="qy_opening_bank" property="qyOpeningBank" javaType="String"/>
        <result column="qy_opening_province" property="qyOpeningProvince" javaType="String"/>
        <result column="qy_opening_city" property="qyOpeningCity" javaType="String"/>
        <result column="qy_change_bank_reason" property="qyChangeBankReason" javaType="String"/>
        <result column="opening_bank_name" property="openingBankName" javaType="String"/>
        <result column="bank_branch_name" property="bankBranchName" javaType="String"/>
        <result column="opening_province_name" property="openingProvinceName" javaType="String"/>
        <result column="opening_city_name" property="openingCityName" javaType="String"/>
        <result column="qy_opening_bank_name" property="qyOpeningBankName" javaType="String"/>
        <result column="qy_bank_branch_name" property="qyBankBranchName" javaType="String"/>
        <result column="qy_opening_province_name" property="qyOpeningProvinceName" javaType="String"/>
        <result column="qy_opening_city_name" property="qyOpeningCityName" javaType="String"/>
        <result column="qy_bank_files" property="qyBankFiles" javaType="String"/>
        <result column="qy_bank_id_number" property="qyBankIdNumber" javaType="String"/>
        <result column="ext1" property="ext1" javaType="String"/>
        <result column="ext2" property="ext2" javaType="String"/>
        <result column="ext3" property="ext3" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="Integer"/>
        <result column="create_user_name" property="createUserName" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap" type="com.bonc.ioc.bzf.business.adjust.vo.BbpmReceivableAdjustPageResultVo">
        <result column="create_time" property="createTime" javaType="Date"/>
                        <result column="adjust_no" property="adjustNo" javaType="String"/>
                        <result column="product_address" property="productAddress" javaType="String"/>
                        <result column="customer_name" property="customerName" javaType="String"/>
                        <result column="customer_id_type" property="customerIdType" javaType="String"/>
                        <result column="customer_id_number" property="customerIdNumber" javaType="String"/>
                        <result column="company" property="company" javaType="String"/>
                        <result column="customer_credit_code" property="customerCreditCode" javaType="String"/>
                        <result column="contract_no" property="contractNo" javaType="String"/>
                        <result column="contract_type" property="contractType" javaType="String"/>
                        <result column="contract_begin_time" property="contractBeginTime" javaType="Date"/>
                        <result column="contract_end_time" property="contractEndTime" javaType="Date"/>
                        <result column="project_id" property="projectId" javaType="String"/>
                        <result column="project_name" property="projectName" javaType="String"/>
                        <result column="adjust_status" property="adjustStatus" javaType="String"/>
                        <result column="refund_status" property="refundStatus" javaType="String"/>
                        <result column="failure_reason" property="failureReason" javaType="String"/>
        <result column="qy_failure_reason" property="qyFailureReason" javaType="String"/>
        <result column="calculation_results" property="calculationResults" javaType="String"/>
                        <result column="adjust_amount" property="adjustAmount" javaType="BigDecimal"/>
                        <result column="adjust_files" property="adjustFiles" javaType="String"/>
                        <result column="amount_handle" property="amountHandle" javaType="String"/>
                        <result column="refund_type" property="refundType" javaType="String"/>
                        <result column="refund_path" property="refundPath" javaType="String"/>
                        <result column="account_holder" property="accountHolder" javaType="String"/>
                        <result column="bank_card" property="bankCard" javaType="String"/>
                        <result column="opening_bank" property="openingBank" javaType="String"/>
                        <result column="opening_province" property="openingProvince" javaType="String"/>
        <result column="opening_city" property="openingCity" javaType="String"/>
        <result column="change_bank_reason" property="changeBankReason" javaType="String"/>
        <result column="bank_files" property="bankFiles" javaType="String"/>
        <result column="bank_id_number" property="bankIdNumber" javaType="String"/>
        <result column="qy_refund_path" property="qyRefundPath" javaType="String"/>
        <result column="qy_account_holder" property="qyAccountHolder" javaType="String"/>
        <result column="qy_bank_card" property="qyBankCard" javaType="String"/>
        <result column="qy_opening_bank" property="qyOpeningBank" javaType="String"/>
        <result column="qy_opening_province" property="qyOpeningProvince" javaType="String"/>
        <result column="qy_opening_city" property="qyOpeningCity" javaType="String"/>
        <result column="qy_change_bank_reason" property="qyChangeBankReason" javaType="String"/>
        <result column="opening_bank_name" property="openingBankName" javaType="String"/>
        <result column="bank_branch_name" property="bankBranchName" javaType="String"/>
        <result column="opening_province_name" property="openingProvinceName" javaType="String"/>
        <result column="opening_city_name" property="openingCityName" javaType="String"/>
        <result column="qy_opening_bank_name" property="qyOpeningBankName" javaType="String"/>
        <result column="qy_bank_branch_name" property="qyBankBranchName" javaType="String"/>
        <result column="qy_opening_province_name" property="qyOpeningProvinceName" javaType="String"/>
        <result column="qy_opening_city_name" property="qyOpeningCityName" javaType="String"/>
        <result column="qy_bank_files" property="qyBankFiles" javaType="String"/>
        <result column="qy_bank_id_number" property="qyBankIdNumber" javaType="String"/>
                        <result column="ext1" property="ext1" javaType="String"/>
                        <result column="ext2" property="ext2" javaType="String"/>
                        <result column="ext3" property="ext3" javaType="String"/>
                        <result column="del_flag" property="delFlag" javaType="Integer"/>
                        <result column="create_user_name" property="createUserName" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.tenant_id
        ,base.cid
        ,base.id
        ,base.adjust_no
        ,base.product_address
        ,base.customer_name
        ,base.customer_id_type
        ,base.customer_id_number
        ,base.company
        ,base.customer_credit_code
        ,base.contract_no
        ,base.contract_type
        ,base.contract_begin_time
        ,base.contract_end_time
        ,base.project_id
        ,base.project_name
        ,base.adjust_status
        ,base.refund_status
        ,base.failure_reason
        ,base.qy_failure_reason
        ,base.calculation_results
        ,base.adjust_amount
        ,base.adjust_files
        ,base.amount_handle
        ,base.refund_type
        ,base.refund_path
        ,base.account_holder
        ,base.bank_card
        ,base.opening_bank
        ,base.bank_branch_code
        ,base.opening_province
        ,base.opening_city
        ,base.change_bank_reason
        ,base.bank_files
        ,base.bank_id_number
        ,base.qy_refund_path
        ,base.qy_account_holder
        ,base.qy_bank_card
        ,base.qy_bank_branch_code
        ,base.qy_opening_bank
        ,base.qy_opening_province
        ,base.qy_opening_city
        ,base.qy_change_bank_reason
        ,base.opening_bank_name
        ,base.bank_branch_name
        ,base.opening_province_name
        ,base.opening_city_name
        ,base.qy_opening_bank_name
        ,base.qy_bank_branch_name
        ,base.qy_opening_province_name
        ,base.qy_opening_city_name
        ,base.qy_bank_files
        ,base.qy_bank_id_number
        ,base.ext1
        ,base.ext2
        ,base.ext3
        ,base.del_flag
        ,base.create_user_name
        ,base.agreement_file
        ,base.change_id
    </sql>
        
    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>,
        examine.create_time as modify_time
        from bbpm_receivable_adjust base
        LEFT JOIN (SELECT adjust_id,max(create_time) create_time FROM bbpm_receivable_adjust_examine  GROUP BY adjust_id) examine ON examine.adjust_id = base.id
        <where>
            base.del_flag = '1'
            and base.change_id is null
            <if test="'' != vo.type and vo.type != null">
                <if test='"1" == vo.type'>
                    and (base.adjust_status = '0' or base.adjust_status = '2' or (base.adjust_status = '3' and (base.refund_status = '1' or base.ext3 = '4')))
                </if>
                <if test='"2" == vo.type'>
                    and (base.adjust_status = '1' or (base.adjust_status = '3' and ((base.refund_status is null or base.refund_status != '1') and (base.ext3  is null or base.ext3 != '4'))))
                </if>
                <if test='"3" == vo.type'>
                    and base.adjust_status = '1'
                </if>
                <if test='"4" == vo.type'>
                    and (base.adjust_status = '2' or base.adjust_status = '3')
                </if>
            </if>
            <if test="'' != vo.projectIdStr and vo.projectIdStr != null">
                and FIND_IN_SET(base.project_id,#{vo.projectIdStr})
            </if>
            <if test="'' != vo.projectId and vo.projectId != null">
                and base.project_id = #{vo.projectId}
            </if>
            <if test="'' != vo.projectName and vo.projectName != null">
                and base.project_name = #{vo.projectName}
            </if>
            <if test="'' != vo.id and vo.id != null">
                and base.id = #{vo.id}
            </if>
            <if test="'' != vo.adjustNo and vo.adjustNo != null">
                and base.adjust_no like concat('%', #{vo.adjustNo}, '%')
            </if>
            <if test="'' != vo.productAddress and vo.productAddress != null">
                and base.product_address like concat('%', #{vo.productAddress}, '%')
            </if>
            <if test="'' != vo.customerName and vo.customerName != null">
                and base.customer_name like concat('%', #{vo.customerName}, '%')
            </if>
            <if test="'' != vo.customerIdType and vo.customerIdType != null">
                and base.customer_id_type = #{vo.customerIdType}
            </if>
            <if test="'' != vo.customerIdNumber and vo.customerIdNumber != null">
                and base.customer_id_number = #{vo.customerIdNumber}
            </if>
            <if test="'' != vo.company and vo.company != null">
                and base.company like concat('%', #{vo.company}, '%')
            </if>
            <if test="'' != vo.customerCreditCode and vo.customerCreditCode != null">
                and base.customer_credit_code = #{vo.customerCreditCode}
            </if>
            <if test="'' != vo.contractNo and vo.contractNo != null">
                and base.contract_no like concat('%', #{vo.contractNo}, '%')
            </if>
            <if test="'' != vo.contractType and vo.contractType != null">
                and base.contract_type = #{vo.contractType}
            </if>
            <if test="vo.contractBeginTime != null">
                and base.contract_begin_time = #{vo.contractBeginTime}
            </if>
            <if test="vo.contractEndTime != null">
                and base.contract_end_time = #{vo.contractEndTime}
            </if>
            <if test="'' != vo.adjustStatus and vo.adjustStatus != null">
                and base.adjust_status = #{vo.adjustStatus}
            </if>
            <if test="'' != vo.refundStatus and vo.refundStatus != null">
                and (base.refund_status = #{vo.refundStatus} or base.ext3 = #{vo.refundStatus})
            </if>
            <if test="'' != vo.failureReason and vo.failureReason != null">
                and base.failure_reason = #{vo.failureReason}
            </if>
            <if test="vo.adjustAmount != null">
                and base.adjust_amount = #{vo.adjustAmount}
            </if>
            <if test="'' != vo.adjustFiles and vo.adjustFiles != null">
                and base.adjust_files = #{vo.adjustFiles}
            </if>
            <if test="'' != vo.amountHandle and vo.amountHandle != null">
                and base.amount_handle = #{vo.amountHandle}
            </if>
            <if test="'' != vo.refundType and vo.refundType != null">
                and base.refund_type = #{vo.refundType}
            </if>
            <if test="'' != vo.refundPath and vo.refundPath != null">
                and base.refund_path = #{vo.refundPath}
            </if>
            <if test="'' != vo.accountHolder and vo.accountHolder != null">
                and base.account_holder = #{vo.accountHolder}
            </if>
            <if test="'' != vo.bankCard and vo.bankCard != null">
                and base.bank_card = #{vo.bankCard}
            </if>
            <if test="'' != vo.openingBank and vo.openingBank != null">
                and base.opening_bank = #{vo.openingBank}
            </if>
            <if test="'' != vo.openingProvince and vo.openingProvince != null">
                and base.opening_province = #{vo.openingProvince}
            </if>
            <if test="'' != vo.openingCity and vo.openingCity != null">
                and base.opening_city = #{vo.openingCity}
            </if>
            <if test="'' != vo.ext1 and vo.ext1 != null">
                and base.ext1 = #{vo.ext1}
            </if>
            <if test="'' != vo.ext2 and vo.ext2 != null">
                and base.ext2 = #{vo.ext2}
            </if>
            <if test="'' != vo.ext3 and vo.ext3 != null">
                and base.ext3 = #{vo.ext3}
            </if>
            <if test="'' != vo.createUserName and vo.createUserName != null">
                and base.create_user_name like concat('%', #{vo.createUserName}, '%')
            </if>
            <if test="'' != vo.createTimeStart and vo.createTimeStart != null and '' != vo.createTimeEnd and vo.createTimeEnd != null">
                and base.create_time <![CDATA[ <= ]]> concat(#{vo.createTimeEnd},' 23:59:59')
                and base.create_time <![CDATA[ >= ]]> concat(#{vo.createTimeStart},' 00:00:00')
            </if>
            <if test="'' != vo.modifyTimeStart and vo.modifyTimeStart != null and '' != vo.modifyTimeEnd and vo.modifyTimeEnd != null">
                and examine.create_time <![CDATA[ <= ]]> concat(#{vo.modifyTimeEnd},' 23:59:59')
                and examine.create_time <![CDATA[ >= ]]> concat(#{vo.modifyTimeStart},' 00:00:00')
            </if>
        </where>
        ORDER BY base.create_time desc
    </select>

    <select id="selectByIdecord" resultMap="BaseResultMapForId">
        select
        <include refid="Base_Column_List"/>
        from bbpm_receivable_adjust base
        <where>
            base.del_flag = '1'
            and base.id = #{id}
        </where>
    </select>

    <select id="selectByCcid" resultMap="BaseResultMapForId">
        select
        <include refid="Base_Column_List"/>
        from bbpm_receivable_adjust base
        <where>
            base.del_flag = '1'
            and base.change_id = #{ccid}
        </where>
        LIMIT 1
    </select>

    <select id="selectTzbh" resultType="java.lang.String">
        SELECT
            SUBSTRING( adjust_no, - 4 ) AS tzbh
        FROM
            bbpm_receivable_adjust
        WHERE adjust_no like concat('%', #{formattedDate}, '%')
        ORDER BY
            create_time DESC
            LIMIT 1
    </select>

    <select id="selectByExportList" resultMap="QueryResultMap">
        SELECT
            base.id,
            base.adjust_no,
            base.amount_handle,
            base.customer_name,
            base.customer_id_number,
            base.company,
            base.customer_credit_code,
            base.contract_no,
            base.contract_type,
            base.contract_begin_time,
            base.contract_end_time,
            base.refund_type,
            base.project_id,
            base.project_name,
            base.calculation_results
        FROM
            bbpm_receivable_adjust base
        <where>
            base.del_flag = '1'
            and base.change_id is null
            <if test="'' != vo.type and vo.type != null">
                <if test='"1" == vo.type'>
                    and (base.adjust_status = '0' or base.adjust_status = '2' or (base.adjust_status = '3' and (base.refund_status = '1' or base.ext3 = '4')))
                </if>
                <if test='"2" == vo.type'>
                    and (base.adjust_status = '1' or (base.adjust_status = '3' and ((base.refund_status is null or base.refund_status != '1') and (base.ext3  is null or base.ext3 != '4'))))
                </if>
                <if test='"3" == vo.type'>
                    and base.adjust_status = '1'
                </if>
                <if test='"4" == vo.type'>
                    and (base.adjust_status = '2' or base.adjust_status = '3')
                </if>
            </if>
            <if test="'' != vo.projectIdStr and vo.projectIdStr != null">
                and FIND_IN_SET(base.project_id,#{vo.projectIdStr})
            </if>
        </where>
        ORDER BY base.create_time desc
    </select>

    <update id="updateForGrTk">
        UPDATE bbpm_receivable_adjust SET refund_status = #{vo.paymentStatus},failure_reason = #{vo.remark} WHERE ext1 = #{vo.paymentCode}
    </update>

    <update id="updateForQyTk">
        UPDATE bbpm_receivable_adjust SET ext3 = #{vo.paymentStatus},qy_failure_reason = #{vo.remark} WHERE ext2 = #{vo.paymentCode}
    </update>

    <select id="selectForTj" resultType="com.bonc.ioc.bzf.business.adjust.vo.AdjustTj">
        SELECT
            SUM(
                    CASE

                        WHEN base.del_flag = '1'
                            AND (base.adjust_status = '0' OR base.adjust_status = '2' OR (base.adjust_status = '3' AND ( base.refund_status = '1' OR base.ext3 = '4' ))) THEN
                            1 ELSE 0
                        END
            ) AS tzdb,
            SUM(
                    CASE

                        WHEN base.del_flag = '1'
                            and (base.adjust_status = '1' or (base.adjust_status = '3' and ((base.refund_status is null or base.refund_status != '1') and (base.ext3  is null or base.ext3 != '4')))) THEN
                            1 ELSE 0
                        END
            ) AS tzyb,
            SUM(
                    CASE

                        WHEN base.del_flag = '1'
                            and base.adjust_status = '1' THEN
                            1 ELSE 0
                        END
            ) AS shdb,
            SUM(
                    CASE

                        WHEN base.del_flag = '1'
                            and (base.adjust_status = '2' or base.adjust_status = '3') THEN
                            1 ELSE 0
                        END
            ) AS shyb
        FROM
            bbpm_receivable_adjust base
        <where>
            base.del_flag = '1'
            and base.change_id is null
            <if test="'' != vo.projectIdStr and vo.projectIdStr != null">
                and FIND_IN_SET(base.project_id,#{vo.projectIdStr})
            </if>
        </where>
    </select>

    <select id="selectContractForAdjustId"  resultType="java.lang.String">
        SELECT
            contract_no as contractNo
        FROM
            bbpm_receivable_adjust base
        WHERE
            base.del_flag = '1'
            AND (base.adjust_status = '0' or base.adjust_status = '1' or base.adjust_status = '2' )
        <if test="'' != id and id != null">
            and base.id != #{id}
        </if>
        <if test="'' != contractNo and contractNo != null">
            and base.contract_no = #{contractNo}
        </if>
    </select>

    <update id="updateAdjustStatus">
        update bbpm_receivable_adjust set adjust_status = #{status} where id = #{id}
    </update>
</mapper>
