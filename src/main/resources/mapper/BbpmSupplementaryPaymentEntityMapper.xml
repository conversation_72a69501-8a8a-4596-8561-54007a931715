<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bonc.ioc.bzf.business.supplementary.dao.BbpmSupplementaryPaymentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bonc.ioc.bzf.business.supplementary.entity.BbpmSupplementaryPaymentEntity">
        <id column="payment_id" property="paymentId" javaType="String"/>
        <result column="create_user" property="createUser" javaType="String"/>
        <result column="create_time" property="createTime" javaType="Date"/>
        <result column="modify_user" property="modifyUser" javaType="String"/>
        <result column="modify_time" property="modifyTime" javaType="Date"/>
        <result column="cid" property="cid" javaType="Long"/>
        <result column="tenant_id" property="tenantId" javaType="Long"/>
        <result column="object_version_number" property="objectVersionNumber" javaType="Integer"/>
        <result column="parent_id" property="parentId" javaType="String"/>
        <result column="payment_type" property="paymentType" javaType="String"/>
        <result column="expense_item" property="expenseItem" javaType="String"/>
        <result column="expense_item_money" property="expenseItemMoney" javaType="String"/>
        <result column="payment_cycle_code" property="paymentCycleCode" javaType="String"/>
        <result column="payment_begin_time" property="paymentBeginTime" javaType="java.time.LocalDate" jdbcType="DATE"/>
        <result column="payment_end_time" property="paymentEndTime" javaType="java.time.LocalDate" jdbcType="DATE"/>
        <result column="supplementary_time" property="supplementaryTime" javaType="java.time.LocalDate"
                jdbcType="DATE"/>
        <result column="charge_standard_code" property="chargeStandardCode" javaType="String"/>
        <result column="charge_standard_money" property="chargeStandardMoney" javaType="String"/>
        <result column="charge_standard_percent" property="chargeStandardPercent" javaType="String"/>
        <result column="charge_remark" property="chargeRemark" javaType="String"/>
        <result column="pay_mode" property="payMode" javaType="String"/>
        <result column="pay_type" property="payType" javaType="String"/>
        <result column="pay_money" property="payMoney" javaType="String"/>
        <result column="pay_percent" property="payPercent" javaType="String"/>
        <result column="serial" property="serial" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="String"/>
    </resultMap>

    <!-- 自定义查询结果 -->
    <resultMap id="QueryResultMap"
               type="com.bonc.ioc.bzf.business.supplementary.vo.BbpmSupplementaryPaymentPageResultVo">
        <result column="parent_id" property="parentId" javaType="String"/>
        <result column="payment_type" property="paymentType" javaType="String"/>
        <result column="expense_item" property="expenseItem" javaType="String"/>
        <result column="expense_item_money" property="expenseItemMoney" javaType="String"/>
        <result column="payment_cycle_code" property="paymentCycleCode" javaType="String"/>
        <result column="payment_begin_time" property="paymentBeginTime" javaType="java.time.LocalDate" jdbcType="DATE"/>
        <result column="payment_end_time" property="paymentEndTime" javaType="java.time.LocalDate" jdbcType="DATE"/>
        <result column="supplementary_time" property="supplementaryTime" javaType="java.time.LocalDate"
                jdbcType="DATE"/>
        <result column="charge_standard_code" property="chargeStandardCode" javaType="String"/>
        <result column="charge_standard_money" property="chargeStandardMoney" javaType="String"/>
        <result column="charge_standard_percent" property="chargeStandardPercent" javaType="String"/>
        <result column="charge_remark" property="chargeRemark" javaType="String"/>
        <result column="pay_mode" property="payMode" javaType="String"/>
        <result column="pay_type" property="payType" javaType="String"/>
        <result column="pay_money" property="payMoney" javaType="String"/>
        <result column="pay_percent" property="payPercent" javaType="String"/>
        <result column="serial" property="serial" javaType="String"/>
        <result column="del_flag" property="delFlag" javaType="String"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        base.object_version_number
        ,base.create_user
        ,base.create_time
        ,base.modify_user
        ,base.modify_time
        ,base.cid
        ,base.tenant_id
        ,base.payment_id
        ,base.parent_id
        ,base.payment_type
        ,base.expense_item
        ,base.expense_item_money
        ,base.payment_cycle_code
        ,base.payment_begin_time
        ,base.payment_end_time
        ,base.supplementary_time
        ,base.charge_standard_code
        ,base.charge_standard_money
        ,base.charge_standard_percent
        ,base.charge_remark
        ,base.pay_mode
        ,base.pay_type
        ,base.pay_money
        ,base.pay_percent
        ,base.serial
        ,base.del_flag
    </sql>

    <select id="selectByPageCustom" resultMap="QueryResultMap">
        select
        <include refid="Base_Column_List"/>
        from bbpm_supplementary_payment base
        <where>
            <if test="'' != vo.paymentId and vo.paymentId != null">
                and base.payment_id = #{vo.paymentId}
            </if>
            <if test="'' != vo.parentId and vo.parentId != null">
                and base.parent_id = #{vo.parentId}
            </if>
            <if test="'' != vo.paymentType and vo.paymentType != null">
                and base.payment_type = #{vo.paymentType}
            </if>
            <if test="'' != vo.expenseItem and vo.expenseItem != null">
                and base.expense_item = #{vo.expenseItem}
            </if>
            <if test="'' != vo.expenseItemMoney and vo.expenseItemMoney != null">
                and base.expense_item_money = #{vo.expenseItemMoney}
            </if>
            <if test="'' != vo.paymentCycleCode and vo.paymentCycleCode != null">
                and base.payment_cycle_code = #{vo.paymentCycleCode}
            </if>
            <if test="vo.paymentBeginTime != null">
                and base.payment_begin_time = #{vo.paymentBeginTime}
            </if>
            <if test="vo.paymentEndTime != null">
                and base.payment_end_time = #{vo.paymentEndTime}
            </if>
            <if test="vo.supplementaryTime != null">
                and base.supplementary_time = #{vo.supplementaryTime}
            </if>
            <if test="'' != vo.chargeStandardCode and vo.chargeStandardCode != null">
                and base.charge_standard_code = #{vo.chargeStandardCode}
            </if>
            <if test="'' != vo.chargeStandardMoney and vo.chargeStandardMoney != null">
                and base.charge_standard_money = #{vo.chargeStandardMoney}
            </if>
            <if test="'' != vo.chargeStandardPercent and vo.chargeStandardPercent != null">
                and base.charge_standard_percent = #{vo.chargeStandardPercent}
            </if>
            <if test="'' != vo.chargeRemark and vo.chargeRemark != null">
                and base.charge_remark = #{vo.chargeRemark}
            </if>
            <if test="'' != vo.payMode and vo.payMode != null">
                and base.pay_mode = #{vo.payMode}
            </if>
            <if test="'' != vo.payType and vo.payType != null">
                and base.pay_type = #{vo.payType}
            </if>
            <if test="'' != vo.payMoney and vo.payMoney != null">
                and base.pay_money = #{vo.payMoney}
            </if>
            <if test="'' != vo.payPercent and vo.payPercent != null">
                and base.pay_percent = #{vo.payPercent}
            </if>
            <if test="'' != vo.serial and vo.serial != null">
                and base.serial = #{vo.serial}
            </if>
            <if test="'' != vo.delFlag and vo.delFlag != null">
                and base.del_flag = #{vo.delFlag}
            </if>
        </where>
    </select>

    <update id="updateDelFlagByParentId">
        UPDATE bbpm_supplementary_payment SET
        del_flag = #{delFlag}
        WHERE parent_id = #{parentId}
    </update>

    <delete id="deleteByParentId">
        DELETE FROM bbpm_supplementary_payment
        WHERE parent_id = #{parentId}
    </delete>

    <select id="selectListByParentId"
            resultType="com.bonc.ioc.bzf.business.supplementary.vo.BbpmSupplementaryPaymentVo">
        SELECT
        base.*
        FROM bbpm_supplementary_payment base
        WHERE
        base.del_flag = '1'
        AND
        base.parent_id = #{parentId}
    </select>
</mapper>
